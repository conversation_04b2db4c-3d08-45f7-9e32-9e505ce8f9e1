import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { BookOpen, Target, Trophy, Users, Zap, ArrowRight, Star, Award } from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const features = [
    {
      icon: BookOpen,
      title: 'Complete Syllabus',
      description: 'Comprehensive coverage of GATE ME 2025 syllabus',
      color: '#1e40af'
    },
    {
      icon: Target,
      title: 'Mock Tests',
      description: 'Practice with GATE-pattern mock tests',
      color: '#dc2626'
    },
    {
      icon: Trophy,
      title: 'AI Tutor',
      description: 'Get personalized guidance from AI',
      color: '#7c3aed'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Connect with fellow GATE aspirants',
      color: '#059669'
    }
  ];

  const stats = [
    { number: '50K+', label: 'Students' },
    { number: '95%', label: 'Success Rate' },
    { number: '1000+', label: 'Questions' },
    { number: '4.8★', label: 'Rating' }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1e40af', '#1e3a8a', '#1d4ed8']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <View style={styles.logo}>
              <BookOpen size={32} color="#ffffff" />
            </View>
            <Text style={styles.logoText}>GATE ME Prep</Text>
          </View>
          <View style={styles.badge}>
            <Star size={12} color="#f59e0b" fill="#f59e0b" />
            <Text style={styles.badgeText}>2025</Text>
          </View>
        </View>

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>
              Master GATE{'\n'}Mechanical Engineering
            </Text>
            <Text style={styles.heroSubtitle}>
              Your complete preparation companion for GATE ME 2025 with AI-powered learning, mock tests, and expert guidance.
            </Text>
            
            {/* Stats */}
            <View style={styles.statsContainer}>
              {stats.map((stat, index) => (
                <View key={index} style={styles.statItem}>
                  <Text style={styles.statNumber}>{stat.number}</Text>
                  <Text style={styles.statLabel}>{stat.label}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Hero Image */}
          <View style={styles.heroImageContainer}>
            <Image 
              source={{ uri: 'https://images.pexels.com/photos/256541/pexels-photo-256541.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop' }}
              style={styles.heroImage}
            />
            <View style={styles.imageOverlay}>
              <View style={styles.floatingCard}>
                <Award size={16} color="#f59e0b" />
                <Text style={styles.floatingText}>Top Ranked App</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <Text style={styles.featuresTitle}>Everything You Need to Succeed</Text>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <View key={index} style={styles.featureCard}>
                <View style={[styles.featureIcon, { backgroundColor: feature.color + '15' }]}>
                  <feature.icon size={20} color={feature.color} />
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Testimonial */}
        <View style={styles.testimonialSection}>
          <View style={styles.testimonialCard}>
            <View style={styles.testimonialHeader}>
              <Image 
                source={{ uri: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' }}
                style={styles.testimonialAvatar}
              />
              <View style={styles.testimonialInfo}>
                <Text style={styles.testimonialName}>Rahul Sharma</Text>
                <Text style={styles.testimonialRole}>GATE ME AIR 23</Text>
                <View style={styles.testimonialRating}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} size={12} color="#f59e0b" fill="#f59e0b" />
                  ))}
                </View>
              </View>
            </View>
            <Text style={styles.testimonialText}>
              "This app transformed my GATE preparation. The AI tutor and mock tests were game-changers!"
            </Text>
          </View>
        </View>

        {/* CTA Buttons */}
        <View style={styles.ctaSection}>
          <TouchableOpacity 
            style={styles.primaryButton}
            onPress={() => router.push('/auth/signup')}
          >
            <Text style={styles.primaryButtonText}>Get Started Free</Text>
            <ArrowRight size={20} color="#ffffff" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.secondaryButton}
            onPress={() => router.push('/auth/signin')}
          >
            <Text style={styles.secondaryButtonText}>Already have an account? Sign In</Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Join thousands of successful GATE aspirants
          </Text>
          <View style={styles.footerBadges}>
            <View style={styles.footerBadge}>
              <Zap size={12} color="#f59e0b" />
              <Text style={styles.footerBadgeText}>AI Powered</Text>
            </View>
            <View style={styles.footerBadge}>
              <Trophy size={12} color="#059669" />
              <Text style={styles.footerBadgeText}>Proven Results</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 20,
    paddingBottom: 30,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 48,
    height: 48,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#ffffff',
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  badgeText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  heroSection: {
    marginBottom: 40,
  },
  heroContent: {
    marginBottom: 30,
  },
  heroTitle: {
    fontSize: 36,
    fontFamily: 'RobotoSlab-Bold',
    color: '#ffffff',
    lineHeight: 44,
    marginBottom: 16,
  },
  heroSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
    lineHeight: 24,
    marginBottom: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#ffffff',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#e0e7ff',
    marginTop: 4,
  },
  heroImageContainer: {
    position: 'relative',
    height: 200,
    borderRadius: 20,
    overflow: 'hidden',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  floatingCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  floatingText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  featuresSection: {
    marginBottom: 40,
  },
  featuresTitle: {
    fontSize: 24,
    fontFamily: 'RobotoSlab-Bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 24,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  featureCard: {
    width: (width - 56) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
    textAlign: 'center',
    lineHeight: 16,
  },
  testimonialSection: {
    marginBottom: 40,
  },
  testimonialCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
  },
  testimonialHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  testimonialAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  testimonialInfo: {
    flex: 1,
  },
  testimonialName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  testimonialRole: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
    marginTop: 2,
  },
  testimonialRating: {
    flexDirection: 'row',
    gap: 2,
    marginTop: 4,
  },
  testimonialText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  ctaSection: {
    marginBottom: 30,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    borderRadius: 16,
    marginBottom: 16,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1e40af',
  },
  secondaryButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#e0e7ff',
  },
  footer: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  footerText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
    marginBottom: 12,
  },
  footerBadges: {
    flexDirection: 'row',
    gap: 16,
  },
  footerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  footerBadgeText: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#e0e7ff',
  },
});