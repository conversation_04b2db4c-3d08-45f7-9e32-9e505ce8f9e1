/*
  # Study Sessions and Progress Tracking Schema
  
  1. Tables Created
    - study_sessions: Individual study sessions with detailed tracking
    - daily_goals: User's daily study goals and targets
    - study_streaks: Track consecutive study days and streaks
    - learning_paths: Personalized learning paths for users
    - study_notes: User notes for topics and subtopics
    - study_reminders: Scheduled study reminders
    
  2. Features
    - Comprehensive study session tracking
    - Goal setting and achievement monitoring
    - Streak tracking and gamification
    - Personalized learning path recommendations
    - Note-taking system integrated with topics
    - Smart reminder system
*/

-- Study sessions tracking
CREATE TABLE IF NOT EXISTS study_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  subtopic_id uuid REFERENCES subtopics(id),
  session_type text DEFAULT 'study' CHECK (session_type IN ('study', 'revision', 'practice', 'test', 'video')),
  started_at timestamptz DEFAULT now(),
  ended_at timestamptz,
  duration_minutes integer DEFAULT 0,
  focus_score integer DEFAULT 5 CHECK (focus_score >= 1 AND focus_score <= 10),
  productivity_rating integer DEFAULT 5 CHECK (productivity_rating >= 1 AND productivity_rating <= 10),
  concepts_learned text[] DEFAULT '{}',
  difficulties_faced text[] DEFAULT '{}',
  session_notes text,
  resources_used text[] DEFAULT '{}',
  break_count integer DEFAULT 0,
  total_break_minutes integer DEFAULT 0,
  device_used text DEFAULT 'mobile',
  location text,
  mood_before text CHECK (mood_before IN ('excited', 'motivated', 'neutral', 'tired', 'stressed')),
  mood_after text CHECK (mood_after IN ('accomplished', 'satisfied', 'neutral', 'frustrated', 'exhausted')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Daily goals and targets
CREATE TABLE IF NOT EXISTS daily_goals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  goal_date date NOT NULL,
  study_hours_target integer DEFAULT 4,
  study_hours_achieved integer DEFAULT 0,
  topics_target integer DEFAULT 2,
  topics_achieved integer DEFAULT 0,
  tests_target integer DEFAULT 1,
  tests_achieved integer DEFAULT 0,
  revision_target integer DEFAULT 1,
  revision_achieved integer DEFAULT 0,
  is_completed boolean DEFAULT false,
  completion_percentage integer DEFAULT 0,
  bonus_goals jsonb DEFAULT '{}', -- Additional custom goals
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, goal_date)
);

-- Study streaks tracking
CREATE TABLE IF NOT EXISTS study_streaks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  streak_type text DEFAULT 'daily' CHECK (streak_type IN ('daily', 'weekly', 'monthly')),
  current_streak integer DEFAULT 0,
  longest_streak integer DEFAULT 0,
  last_activity_date date,
  streak_start_date date,
  longest_streak_start_date date,
  longest_streak_end_date date,
  total_active_days integer DEFAULT 0,
  streak_milestones jsonb DEFAULT '{}', -- Track milestone achievements
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, streak_type)
);

-- Personalized learning paths
CREATE TABLE IF NOT EXISTS learning_paths (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  path_type text DEFAULT 'custom' CHECK (path_type IN ('recommended', 'custom', 'weak-areas', 'exam-focused')),
  subjects_order uuid[] DEFAULT '{}', -- Ordered list of subject IDs
  topics_order uuid[] DEFAULT '{}', -- Ordered list of topic IDs
  estimated_completion_days integer DEFAULT 90,
  difficulty_progression text DEFAULT 'gradual' CHECK (difficulty_progression IN ('easy-first', 'gradual', 'mixed', 'hard-first')),
  daily_time_allocation jsonb DEFAULT '{}', -- Time allocation per subject
  is_active boolean DEFAULT true,
  progress_percentage integer DEFAULT 0,
  started_at timestamptz,
  target_completion_date date,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Study notes for topics and subtopics
CREATE TABLE IF NOT EXISTS study_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  subtopic_id uuid REFERENCES subtopics(id),
  title text NOT NULL,
  content text NOT NULL,
  note_type text DEFAULT 'general' CHECK (note_type IN ('general', 'formula', 'concept', 'doubt', 'tip', 'example')),
  tags text[] DEFAULT '{}',
  is_favorite boolean DEFAULT false,
  is_shared boolean DEFAULT false,
  attachments jsonb DEFAULT '[]', -- File attachments metadata
  formatting jsonb DEFAULT '{}', -- Rich text formatting data
  last_reviewed_at timestamptz,
  review_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Study reminders and notifications
CREATE TABLE IF NOT EXISTS study_reminders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  reminder_type text DEFAULT 'study' CHECK (reminder_type IN ('study', 'test', 'revision', 'break', 'goal-check')),
  scheduled_time time NOT NULL,
  days_of_week integer[] DEFAULT '{1,2,3,4,5,6,7}', -- 1=Monday, 7=Sunday
  is_active boolean DEFAULT true,
  is_recurring boolean DEFAULT true,
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  custom_message text,
  notification_methods text[] DEFAULT '{"push"}', -- push, email, sms
  snooze_duration_minutes integer DEFAULT 15,
  max_snooze_count integer DEFAULT 3,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE study_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_reminders ENABLE ROW LEVEL SECURITY;

-- RLS Policies for study_sessions
CREATE POLICY "Users can manage own study sessions"
  ON study_sessions
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for daily_goals
CREATE POLICY "Users can manage own daily goals"
  ON daily_goals
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for study_streaks
CREATE POLICY "Users can manage own study streaks"
  ON study_streaks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for learning_paths
CREATE POLICY "Users can manage own learning paths"
  ON learning_paths
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for study_notes
CREATE POLICY "Users can manage own study notes"
  ON study_notes
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for study_reminders
CREATE POLICY "Users can manage own study reminders"
  ON study_reminders
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON study_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_date ON study_sessions(DATE(started_at));
CREATE INDEX IF NOT EXISTS idx_study_sessions_subject ON study_sessions(subject_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_topic ON study_sessions(topic_id);
CREATE INDEX IF NOT EXISTS idx_daily_goals_user_date ON daily_goals(user_id, goal_date);
CREATE INDEX IF NOT EXISTS idx_study_streaks_user_id ON study_streaks(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_paths_user_id ON learning_paths(user_id);
CREATE INDEX IF NOT EXISTS idx_study_notes_user_id ON study_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_study_notes_subject ON study_notes(subject_id);
CREATE INDEX IF NOT EXISTS idx_study_notes_topic ON study_notes(topic_id);
CREATE INDEX IF NOT EXISTS idx_study_reminders_user_id ON study_reminders(user_id);
CREATE INDEX IF NOT EXISTS idx_study_reminders_active ON study_reminders(is_active);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_study_sessions_updated_at
  BEFORE UPDATE ON study_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_goals_updated_at
  BEFORE UPDATE ON daily_goals
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_study_streaks_updated_at
  BEFORE UPDATE ON study_streaks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_paths_updated_at
  BEFORE UPDATE ON learning_paths
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_study_notes_updated_at
  BEFORE UPDATE ON study_notes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_study_reminders_updated_at
  BEFORE UPDATE ON study_reminders
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();