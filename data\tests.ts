export interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  subject: string;
  topic: string;
  marks: number;
}

export interface Test {
  id: string;
  title: string;
  description: string;
  type: 'mock' | 'subject' | 'topic' | 'previous-year';
  duration: number; // in minutes
  totalMarks: number;
  questions: Question[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  subjects: string[];
  isAttempted: boolean;
  score?: number;
  timeSpent?: number;
  attemptedAt?: Date;
}

export const mockTests: Test[] = [
  {
    id: 'gate-2025-mock-1',
    title: 'GATE 2025 Full Mock Test #1',
    description: 'Complete GATE Mechanical Engineering mock test covering all subjects',
    type: 'mock',
    duration: 180,
    totalMarks: 100,
    difficulty: 'Hard',
    subjects: ['Engineering Mathematics', 'Applied Mechanics', 'Thermal Sciences', 'Manufacturing'],
    isAttempted: false,
    questions: [
      {
        id: 'q1',
        question: 'The eigenvalues of the matrix [[2, 1], [1, 2]] are:',
        options: ['1, 3', '2, 2', '0, 4', '1, 2'],
        correctAnswer: 0,
        explanation: 'For the characteristic equation det(A - λI) = 0, we get (2-λ)² - 1 = 0, which gives λ = 1, 3',
        difficulty: 'Medium',
        subject: 'Engineering Mathematics',
        topic: 'Linear Algebra',
        marks: 2
      },
      {
        id: 'q2',
        question: 'In a simply supported beam with a point load at the center, the maximum bending moment occurs at:',
        options: ['The supports', 'Quarter span', 'Center of the beam', 'Three-quarter span'],
        correctAnswer: 2,
        explanation: 'For a simply supported beam with central point load, maximum bending moment occurs at the center where the load is applied',
        difficulty: 'Easy',
        subject: 'Strength of Materials',
        topic: 'Bending of Beams',
        marks: 1
      }
    ]
  },
  {
    id: 'gate-2024-paper',
    title: 'GATE 2024 Previous Year Paper',
    description: 'Official GATE 2024 Mechanical Engineering question paper',
    type: 'previous-year',
    duration: 180,
    totalMarks: 100,
    difficulty: 'Hard',
    subjects: ['Engineering Mathematics', 'Applied Mechanics', 'Thermal Sciences', 'Manufacturing'],
    isAttempted: true,
    score: 78,
    timeSpent: 165,
    attemptedAt: new Date('2024-01-15'),
    questions: []
  },
  {
    id: 'thermodynamics-test',
    title: 'Thermodynamics Subject Test',
    description: 'Comprehensive test covering all thermodynamics topics',
    type: 'subject',
    duration: 90,
    totalMarks: 50,
    difficulty: 'Medium',
    subjects: ['Thermodynamics'],
    isAttempted: true,
    score: 42,
    timeSpent: 85,
    attemptedAt: new Date('2024-01-10'),
    questions: []
  }
];

export const topicTests: Test[] = [
  {
    id: 'linear-algebra-test',
    title: 'Linear Algebra Practice Test',
    description: 'Test covering matrix operations, eigenvalues, and systems of equations',
    type: 'topic',
    duration: 45,
    totalMarks: 25,
    difficulty: 'Medium',
    subjects: ['Engineering Mathematics'],
    isAttempted: false,
    questions: []
  },
  {
    id: 'heat-transfer-test',
    title: 'Heat Transfer Fundamentals',
    description: 'Conduction, convection, and radiation problems',
    type: 'topic',
    duration: 60,
    totalMarks: 30,
    difficulty: 'Hard',
    subjects: ['Heat Transfer'],
    isAttempted: true,
    score: 26,
    timeSpent: 55,
    attemptedAt: new Date('2024-01-08'),
    questions: []
  }
];

export const getTestsByType = (type: string) => {
  if (type === 'mock') return mockTests.filter(test => test.type === 'mock');
  if (type === 'topic') return topicTests;
  if (type === 'previous-year') return mockTests.filter(test => test.type === 'previous-year');
  return [...mockTests, ...topicTests];
};

export const getTestById = (id: string) => {
  return [...mockTests, ...topicTests].find(test => test.id === id);
};

export const getTestStats = () => {
  const allTests = [...mockTests, ...topicTests];
  const attemptedTests = allTests.filter(test => test.isAttempted);
  const totalTests = allTests.length;
  const averageScore = attemptedTests.length > 0 
    ? Math.round(attemptedTests.reduce((sum, test) => sum + (test.score || 0), 0) / attemptedTests.length)
    : 0;

  return {
    totalTests,
    attemptedTests: attemptedTests.length,
    averageScore,
    mockTestsAttempted: mockTests.filter(test => test.isAttempted).length,
    topicTestsAttempted: topicTests.filter(test => test.isAttempted).length
  };
};