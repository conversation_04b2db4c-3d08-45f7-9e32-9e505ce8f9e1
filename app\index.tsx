import { Redirect } from 'expo-router';
import { useAuth } from '@/components/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';

export default function Index() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (isAuthenticated) {
    return <Redirect href="/(tabs)" />;
  }

  return <Redirect href="/auth/signin" />;
}
