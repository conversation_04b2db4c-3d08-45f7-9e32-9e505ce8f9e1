/*
  # Analytics and Reporting Schema
  
  1. Tables Created
    - user_analytics: Comprehensive user behavior analytics
    - study_analytics: Study pattern and performance analytics
    - test_analytics: Test performance and improvement analytics
    - content_analytics: Content consumption and engagement analytics
    - app_usage_analytics: App usage patterns and feature adoption
    - performance_metrics: System performance and health metrics
    - custom_reports: User-generated custom reports
    - analytics_dashboards: Predefined dashboard configurations
    
  2. Features
    - Comprehensive user behavior tracking
    - Study pattern analysis and insights
    - Test performance analytics
    - Content engagement metrics
    - App usage and feature adoption tracking
    - System performance monitoring
    - Custom reporting capabilities
    - Dashboard configuration management
*/

-- User behavior analytics
CREATE TABLE IF NOT EXISTS user_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  event_type text NOT NULL,
  event_name text NOT NULL,
  event_category text NOT NULL,
  event_data jsonb DEFAULT '{}',
  session_id text,
  page_url text,
  referrer_url text,
  user_agent text,
  device_type text,
  platform text,
  screen_resolution text,
  ip_address inet,
  country text,
  city text,
  timestamp timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Study analytics and patterns
CREATE TABLE IF NOT EXISTS study_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  date date NOT NULL,
  total_study_time_minutes integer DEFAULT 0,
  active_study_time_minutes integer DEFAULT 0,
  break_time_minutes integer DEFAULT 0,
  subjects_studied text[] DEFAULT '{}',
  topics_completed integer DEFAULT 0,
  concepts_learned integer DEFAULT 0,
  average_focus_score decimal(3,2) DEFAULT 0,
  productivity_rating decimal(3,2) DEFAULT 0,
  study_sessions_count integer DEFAULT 0,
  peak_study_hour integer, -- Hour of day when most productive
  study_streak_days integer DEFAULT 0,
  goals_achieved integer DEFAULT 0,
  goals_missed integer DEFAULT 0,
  notes_created integer DEFAULT 0,
  doubts_raised integer DEFAULT 0,
  ai_interactions integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, date)
);

-- Test performance analytics
CREATE TABLE IF NOT EXISTS test_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  test_id uuid REFERENCES tests(id),
  attempt_id uuid REFERENCES user_test_attempts(id),
  test_type text NOT NULL,
  subject_wise_scores jsonb DEFAULT '{}',
  topic_wise_scores jsonb DEFAULT '{}',
  difficulty_wise_performance jsonb DEFAULT '{}',
  time_management_score decimal(3,2) DEFAULT 0,
  accuracy_percentage decimal(5,2) DEFAULT 0,
  speed_score decimal(3,2) DEFAULT 0,
  improvement_from_last_attempt decimal(5,2) DEFAULT 0,
  rank_improvement integer DEFAULT 0,
  weak_areas text[] DEFAULT '{}',
  strong_areas text[] DEFAULT '{}',
  recommended_focus_areas text[] DEFAULT '{}',
  test_date date NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Content engagement analytics
CREATE TABLE IF NOT EXISTS content_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id),
  content_type text NOT NULL,
  engagement_type text NOT NULL CHECK (engagement_type IN ('view', 'complete', 'bookmark', 'share', 'rate', 'download')),
  engagement_duration_seconds integer DEFAULT 0,
  completion_percentage integer DEFAULT 0,
  quality_watched text,
  playback_speed decimal(3,2) DEFAULT 1.0,
  device_type text,
  network_type text,
  engagement_score decimal(3,2) DEFAULT 0,
  retention_score decimal(3,2) DEFAULT 0,
  interaction_count integer DEFAULT 0,
  timestamp timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- App usage and feature adoption analytics
CREATE TABLE IF NOT EXISTS app_usage_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  date date NOT NULL,
  session_count integer DEFAULT 0,
  total_session_duration_minutes integer DEFAULT 0,
  average_session_duration_minutes decimal(6,2) DEFAULT 0,
  features_used text[] DEFAULT '{}',
  screens_visited text[] DEFAULT '{}',
  actions_performed jsonb DEFAULT '{}',
  crashes_count integer DEFAULT 0,
  errors_count integer DEFAULT 0,
  app_version text,
  os_version text,
  device_model text,
  network_type text,
  battery_level integer,
  storage_used_mb decimal(8,2),
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, date)
);

-- System performance metrics
CREATE TABLE IF NOT EXISTS performance_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_type text NOT NULL CHECK (metric_type IN ('response_time', 'error_rate', 'throughput', 'availability', 'resource_usage')),
  metric_name text NOT NULL,
  metric_value decimal(12,4) NOT NULL,
  metric_unit text,
  service_name text,
  endpoint text,
  status_code integer,
  error_type text,
  additional_data jsonb DEFAULT '{}',
  timestamp timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Custom user reports
CREATE TABLE IF NOT EXISTS custom_reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  report_name text NOT NULL,
  report_description text,
  report_type text NOT NULL CHECK (report_type IN ('study-progress', 'test-performance', 'time-analysis', 'subject-wise', 'custom')),
  report_config jsonb NOT NULL,
  filters jsonb DEFAULT '{}',
  date_range jsonb DEFAULT '{}',
  is_scheduled boolean DEFAULT false,
  schedule_frequency text CHECK (schedule_frequency IN ('daily', 'weekly', 'monthly')),
  last_generated_at timestamptz,
  is_shared boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Analytics dashboards configuration
CREATE TABLE IF NOT EXISTS analytics_dashboards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  dashboard_name text NOT NULL,
  dashboard_type text DEFAULT 'personal' CHECK (dashboard_type IN ('personal', 'shared', 'template')),
  layout_config jsonb NOT NULL,
  widgets jsonb DEFAULT '[]',
  filters jsonb DEFAULT '{}',
  refresh_interval_minutes integer DEFAULT 60,
  is_default boolean DEFAULT false,
  is_public boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE user_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_usage_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_dashboards ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_analytics
CREATE POLICY "Users can read own analytics"
  ON user_analytics
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own analytics"
  ON user_analytics
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for study_analytics
CREATE POLICY "Users can read own study analytics"
  ON study_analytics
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own study analytics"
  ON study_analytics
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own study analytics"
  ON study_analytics
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for test_analytics
CREATE POLICY "Users can read own test analytics"
  ON test_analytics
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own test analytics"
  ON test_analytics
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for content_analytics
CREATE POLICY "Users can read own content analytics"
  ON content_analytics
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own content analytics"
  ON content_analytics
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for app_usage_analytics
CREATE POLICY "Users can read own app usage analytics"
  ON app_usage_analytics
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own app usage analytics"
  ON app_usage_analytics
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own app usage analytics"
  ON app_usage_analytics
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for performance_metrics (admin only)
CREATE POLICY "No access to performance metrics"
  ON performance_metrics
  FOR ALL
  TO authenticated
  USING (false);

-- RLS Policies for custom_reports
CREATE POLICY "Users can manage own custom reports"
  ON custom_reports
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for analytics_dashboards
CREATE POLICY "Users can manage own dashboards"
  ON analytics_dashboards
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Anyone can read public dashboards"
  ON analytics_dashboards
  FOR SELECT
  TO authenticated
  USING (is_public = true);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_analytics_user_id ON user_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_analytics_event_type ON user_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_user_analytics_timestamp ON user_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_study_analytics_user_date ON study_analytics(user_id, date);
CREATE INDEX IF NOT EXISTS idx_study_analytics_date ON study_analytics(date);
CREATE INDEX IF NOT EXISTS idx_test_analytics_user_id ON test_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_test_analytics_test_date ON test_analytics(test_date);
CREATE INDEX IF NOT EXISTS idx_content_analytics_user_id ON content_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_content_analytics_content_id ON content_analytics(content_id);
CREATE INDEX IF NOT EXISTS idx_content_analytics_timestamp ON content_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_app_usage_analytics_user_date ON app_usage_analytics(user_id, date);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_custom_reports_user_id ON custom_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_dashboards_user_id ON analytics_dashboards(user_id);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_custom_reports_updated_at
  BEFORE UPDATE ON custom_reports
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analytics_dashboards_updated_at
  BEFORE UPDATE ON analytics_dashboards
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();