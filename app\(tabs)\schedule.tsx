import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Calendar, Clock, Plus, BookOpen, FileText, Target, Bell, ChevronLeft, ChevronRight, CircleCheck as CheckCircle2, CircleAlert as AlertCircle, Play } from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function ScheduleScreen() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('week'); // 'day', 'week', 'month'

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  const todaySchedule = [
    {
      id: 1,
      time: '09:00 AM',
      duration: 120,
      title: 'Linear Algebra Study Session',
      type: 'study',
      subject: 'Engineering Mathematics',
      status: 'completed',
      progress: 100
    },
    {
      id: 2,
      time: '11:30 AM',
      duration: 90,
      title: 'Thermodynamics Mock Test',
      type: 'test',
      subject: 'Thermodynamics',
      status: 'in-progress',
      progress: 65
    },
    {
      id: 3,
      time: '02:00 PM',
      duration: 60,
      title: 'Fluid Mechanics Video Lecture',
      type: 'video',
      subject: 'Fluid Mechanics',
      status: 'pending',
      progress: 0
    },
    {
      id: 4,
      time: '04:00 PM',
      duration: 45,
      title: 'Strength of Materials Revision',
      type: 'revision',
      subject: 'Strength of Materials',
      status: 'pending',
      progress: 0
    },
    {
      id: 5,
      time: '06:00 PM',
      duration: 30,
      title: 'Daily Challenge Quiz',
      type: 'challenge',
      subject: 'Mixed Topics',
      status: 'pending',
      progress: 0
    }
  ];

  const weeklyGoals = [
    {
      title: 'Complete 15 Study Sessions',
      current: 8,
      target: 15,
      progress: 53
    },
    {
      title: 'Attempt 5 Mock Tests',
      current: 2,
      target: 5,
      progress: 40
    },
    {
      title: 'Watch 10 Video Lectures',
      current: 6,
      target: 10,
      progress: 60
    }
  ];

  const upcomingDeadlines = [
    {
      title: 'GATE 2025 Registration Deadline',
      date: 'Aug 24, 2024',
      daysLeft: 15,
      type: 'critical'
    },
    {
      title: 'Mock Test Series Enrollment',
      date: 'Sep 15, 2024',
      daysLeft: 37,
      type: 'important'
    },
    {
      title: 'Syllabus Completion Target',
      date: 'Dec 31, 2024',
      daysLeft: 144,
      type: 'goal'
    }
  ];

  const studyStreak = {
    current: 12,
    best: 28,
    thisWeek: [true, true, false, true, true, true, false]
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'study': return BookOpen;
      case 'test': return FileText;
      case 'video': return Play;
      case 'revision': return Target;
      case 'challenge': return Target;
      default: return BookOpen;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'study': return '#1e40af';
      case 'test': return '#dc2626';
      case 'video': return '#7c3aed';
      case 'revision': return '#059669';
      case 'challenge': return '#ea580c';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#059669';
      case 'in-progress': return '#ea580c';
      case 'pending': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Study Schedule</Text>
        <Text style={styles.subtitle}>Plan your GATE preparation</Text>
      </View>

      {/* View Mode Selector */}
      <View style={styles.viewModeContainer}>
        {['day', 'week', 'month'].map((mode) => (
          <TouchableOpacity
            key={mode}
            style={[
              styles.viewModeButton,
              viewMode === mode && styles.activeViewModeButton
            ]}
            onPress={() => setViewMode(mode)}
          >
            <Text
              style={[
                styles.viewModeText,
                viewMode === mode && styles.activeViewModeText
              ]}
            >
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {/* Calendar Header */}
        <View style={styles.calendarHeader}>
          <TouchableOpacity style={styles.navButton}>
            <ChevronLeft size={20} color="#6b7280" />
          </TouchableOpacity>
          <Text style={styles.calendarTitle}>
            {months[selectedDate.getMonth()]} {selectedDate.getFullYear()}
          </Text>
          <TouchableOpacity style={styles.navButton}>
            <ChevronRight size={20} color="#6b7280" />
          </TouchableOpacity>
        </View>

        {/* Study Streak */}
        <View style={styles.streakContainer}>
          <Text style={styles.sectionTitle}>Study Streak</Text>
          <View style={styles.streakCard}>
            <View style={styles.streakStats}>
              <View style={styles.streakStat}>
                <Text style={styles.streakNumber}>{studyStreak.current}</Text>
                <Text style={styles.streakLabel}>Current</Text>
              </View>
              <View style={styles.streakStat}>
                <Text style={styles.streakNumber}>{studyStreak.best}</Text>
                <Text style={styles.streakLabel}>Best</Text>
              </View>
            </View>
            <View style={styles.streakWeek}>
              {weekDays.map((day, index) => (
                <View key={index} style={styles.streakDay}>
                  <Text style={styles.streakDayText}>{day}</Text>
                  <View style={[
                    styles.streakDot,
                    studyStreak.thisWeek[index] && styles.streakDotActive
                  ]} />
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Today's Schedule */}
        <View style={styles.scheduleContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Schedule</Text>
            <TouchableOpacity style={styles.addButton}>
              <Plus size={20} color="#1e40af" />
            </TouchableOpacity>
          </View>
          
          {todaySchedule.map((item) => {
            const TypeIcon = getTypeIcon(item.type);
            return (
              <TouchableOpacity key={item.id} style={styles.scheduleItem}>
                <View style={styles.scheduleTime}>
                  <Text style={styles.timeText}>{item.time}</Text>
                  <Text style={styles.durationText}>{formatTime(item.duration)}</Text>
                </View>
                <View style={styles.scheduleContent}>
                  <View style={styles.scheduleHeader}>
                    <View style={[styles.typeIcon, { backgroundColor: getTypeColor(item.type) + '15' }]}>
                      <TypeIcon size={16} color={getTypeColor(item.type)} />
                    </View>
                    <View style={styles.scheduleInfo}>
                      <Text style={styles.scheduleTitle}>{item.title}</Text>
                      <Text style={styles.scheduleSubject}>{item.subject}</Text>
                    </View>
                    <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
                  </View>
                  {item.progress > 0 && (
                    <View style={styles.progressContainer}>
                      <View style={styles.progressBar}>
                        <View style={[styles.progressFill, { width: `${item.progress}%` }]} />
                      </View>
                      <Text style={styles.progressText}>{item.progress}%</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Weekly Goals */}
        <View style={styles.goalsContainer}>
          <Text style={styles.sectionTitle}>Weekly Goals</Text>
          <View style={styles.goalsCard}>
            {weeklyGoals.map((goal, index) => (
              <View key={index} style={styles.goalItem}>
                <View style={styles.goalInfo}>
                  <Text style={styles.goalTitle}>{goal.title}</Text>
                  <Text style={styles.goalProgress}>
                    {goal.current}/{goal.target} completed
                  </Text>
                </View>
                <View style={styles.goalProgressContainer}>
                  <View style={styles.goalProgressBar}>
                    <View style={[styles.goalProgressFill, { width: `${goal.progress}%` }]} />
                  </View>
                  <Text style={styles.goalPercentage}>{goal.progress}%</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Upcoming Deadlines */}
        <View style={styles.deadlinesContainer}>
          <Text style={styles.sectionTitle}>Upcoming Deadlines</Text>
          <View style={styles.deadlinesCard}>
            {upcomingDeadlines.map((deadline, index) => (
              <View key={index} style={styles.deadlineItem}>
                <View style={styles.deadlineIcon}>
                  {deadline.type === 'critical' ? (
                    <AlertCircle size={20} color="#dc2626" />
                  ) : (
                    <Calendar size={20} color="#1e40af" />
                  )}
                </View>
                <View style={styles.deadlineInfo}>
                  <Text style={styles.deadlineTitle}>{deadline.title}</Text>
                  <Text style={styles.deadlineDate}>{deadline.date}</Text>
                </View>
                <View style={[
                  styles.deadlineBadge,
                  { backgroundColor: deadline.type === 'critical' ? '#fef2f2' : '#eff6ff' }
                ]}>
                  <Text style={[
                    styles.deadlineDays,
                    { color: deadline.type === 'critical' ? '#dc2626' : '#1e40af' }
                  ]}>
                    {deadline.daysLeft} days
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity style={styles.quickActionCard}>
              <BookOpen size={24} color="#1e40af" />
              <Text style={styles.quickActionText}>Add Study Session</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickActionCard}>
              <FileText size={24} color="#dc2626" />
              <Text style={styles.quickActionText}>Schedule Test</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickActionCard}>
              <Bell size={24} color="#ea580c" />
              <Text style={styles.quickActionText}>Set Reminder</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickActionCard}>
              <Target size={24} color="#059669" />
              <Text style={styles.quickActionText}>Create Goal</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Study Statistics */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>This Week's Statistics</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Clock size={20} color="#1e40af" />
              <Text style={styles.statNumber}>18.5h</Text>
              <Text style={styles.statLabel}>Study Time</Text>
            </View>
            <View style={styles.statCard}>
              <FileText size={20} color="#dc2626" />
              <Text style={styles.statNumber}>5</Text>
              <Text style={styles.statLabel}>Tests Taken</Text>
            </View>
            <View style={styles.statCard}>
              <CheckCircle2 size={20} color="#059669" />
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>Topics Done</Text>
            </View>
            <View style={styles.statCard}>
              <Target size={20} color="#7c3aed" />
              <Text style={styles.statNumber}>85%</Text>
              <Text style={styles.statLabel}>Goal Progress</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  viewModeContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    alignItems: 'center',
  },
  activeViewModeButton: {
    backgroundColor: '#1e40af',
  },
  viewModeText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activeViewModeText: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  navButton: {
    width: 40,
    height: 40,
    backgroundColor: '#ffffff',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  calendarTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
  },
  streakContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  streakCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  streakStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  streakStat: {
    alignItems: 'center',
  },
  streakNumber: {
    fontSize: 24,
    fontFamily: 'RobotoSlab-Bold',
    color: '#1e40af',
  },
  streakLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    marginTop: 4,
  },
  streakWeek: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  streakDay: {
    alignItems: 'center',
  },
  streakDayText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    marginBottom: 8,
  },
  streakDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#e5e7eb',
  },
  streakDotActive: {
    backgroundColor: '#1e40af',
  },
  scheduleContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    width: 36,
    height: 36,
    backgroundColor: '#eff6ff',
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scheduleItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  scheduleTime: {
    width: 80,
    marginRight: 16,
  },
  timeText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  durationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  scheduleSubject: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#e5e7eb',
    borderRadius: 2,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#1e40af',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  goalsContainer: {
    marginBottom: 24,
  },
  goalsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  goalItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  goalInfo: {
    marginBottom: 8,
  },
  goalTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  goalProgress: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  goalProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalProgressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
    marginRight: 8,
  },
  goalProgressFill: {
    height: '100%',
    backgroundColor: '#059669',
    borderRadius: 3,
  },
  goalPercentage: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    minWidth: 35,
    textAlign: 'right',
  },
  deadlinesContainer: {
    marginBottom: 24,
  },
  deadlinesCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  deadlineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  deadlineIcon: {
    marginRight: 12,
  },
  deadlineInfo: {
    flex: 1,
  },
  deadlineTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  deadlineDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  deadlineBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  deadlineDays: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
  },
  quickActionsContainer: {
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: (width - 64) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  quickActionText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginTop: 8,
    textAlign: 'center',
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    width: (width - 64) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
});