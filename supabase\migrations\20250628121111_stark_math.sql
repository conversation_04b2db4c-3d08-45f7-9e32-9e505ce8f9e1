/*
  # Tests and Questions Management Schema
  
  1. Tables Created
    - test_categories: Categories for organizing tests
    - tests: Mock tests, topic tests, previous year papers
    - questions: Individual questions with options and explanations
    - test_questions: Junction table linking tests to questions
    - user_test_attempts: Track user test attempts and scores
    - user_question_responses: Track individual question responses
    - question_bookmarks: User bookmarked questions
    
  2. Features
    - Comprehensive test management system
    - Question bank with detailed explanations
    - Multiple test types (mock, topic, previous year)
    - Detailed attempt tracking and analytics
    - Question bookmarking and review system
*/

-- Test categories for organization
CREATE TABLE IF NOT EXISTS test_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  color text DEFAULT '#1e40af',
  icon text DEFAULT 'file-text',
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Tests table (Mock tests, topic tests, etc.)
CREATE TABLE IF NOT EXISTS tests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  category_id uuid REFERENCES test_categories(id),
  test_type text NOT NULL CHECK (test_type IN ('mock', 'subject', 'topic', 'previous-year', 'daily-challenge')),
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  duration_minutes integer NOT NULL DEFAULT 180,
  total_marks integer NOT NULL DEFAULT 100,
  negative_marking boolean DEFAULT true,
  negative_marks_ratio decimal(3,2) DEFAULT 0.33,
  passing_marks integer DEFAULT 40,
  subjects text[] DEFAULT '{}',
  topics text[] DEFAULT '{}',
  instructions text,
  is_published boolean DEFAULT false,
  is_free boolean DEFAULT true,
  max_attempts integer DEFAULT 3,
  show_results_immediately boolean DEFAULT true,
  randomize_questions boolean DEFAULT false,
  created_by uuid REFERENCES users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  published_at timestamptz,
  expires_at timestamptz
);

-- Questions table
CREATE TABLE IF NOT EXISTS questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_text text NOT NULL,
  question_type text DEFAULT 'mcq' CHECK (question_type IN ('mcq', 'numerical', 'maq', 'true-false')),
  options jsonb DEFAULT '[]', -- Array of options for MCQ
  correct_answer jsonb NOT NULL, -- Can be single value or array for MAQ
  explanation text,
  solution_steps text[],
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  marks integer DEFAULT 1,
  negative_marks decimal(3,2) DEFAULT 0.33,
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  subtopic_id uuid REFERENCES subtopics(id),
  tags text[] DEFAULT '{}',
  image_url text,
  formula_used text[],
  concepts_tested text[],
  previous_year_frequency integer DEFAULT 0,
  created_by uuid REFERENCES users(id),
  verified_by uuid REFERENCES users(id),
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Junction table for test-question relationships
CREATE TABLE IF NOT EXISTS test_questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  test_id uuid REFERENCES tests(id) ON DELETE CASCADE,
  question_id uuid REFERENCES questions(id) ON DELETE CASCADE,
  question_order integer NOT NULL,
  marks_override integer, -- Override default question marks for this test
  created_at timestamptz DEFAULT now(),
  UNIQUE(test_id, question_id),
  UNIQUE(test_id, question_order)
);

-- User test attempts
CREATE TABLE IF NOT EXISTS user_test_attempts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  test_id uuid REFERENCES tests(id) ON DELETE CASCADE,
  attempt_number integer NOT NULL DEFAULT 1,
  started_at timestamptz DEFAULT now(),
  submitted_at timestamptz,
  time_spent_minutes integer DEFAULT 0,
  total_questions integer NOT NULL,
  attempted_questions integer DEFAULT 0,
  correct_answers integer DEFAULT 0,
  incorrect_answers integer DEFAULT 0,
  unanswered_questions integer DEFAULT 0,
  total_marks_obtained decimal(6,2) DEFAULT 0,
  percentage_score decimal(5,2) DEFAULT 0,
  rank_achieved integer,
  is_completed boolean DEFAULT false,
  is_submitted boolean DEFAULT false,
  answers jsonb DEFAULT '{}', -- Store user answers
  question_wise_time jsonb DEFAULT '{}', -- Time spent on each question
  review_flags jsonb DEFAULT '{}', -- Questions marked for review
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Individual question responses within test attempts
CREATE TABLE IF NOT EXISTS user_question_responses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  attempt_id uuid REFERENCES user_test_attempts(id) ON DELETE CASCADE,
  question_id uuid REFERENCES questions(id) ON DELETE CASCADE,
  user_answer jsonb, -- User's selected answer(s)
  is_correct boolean DEFAULT false,
  marks_obtained decimal(4,2) DEFAULT 0,
  time_spent_seconds integer DEFAULT 0,
  is_marked_for_review boolean DEFAULT false,
  answered_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Question bookmarks for users
CREATE TABLE IF NOT EXISTS question_bookmarks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  question_id uuid REFERENCES questions(id) ON DELETE CASCADE,
  notes text,
  tags text[] DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, question_id)
);

-- Enable Row Level Security
ALTER TABLE test_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_test_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_question_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE question_bookmarks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for test_categories (public read)
CREATE POLICY "Anyone can read test categories"
  ON test_categories
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for tests (public read for published tests)
CREATE POLICY "Anyone can read published tests"
  ON tests
  FOR SELECT
  TO authenticated
  USING (is_published = true);

CREATE POLICY "Users can read own tests"
  ON tests
  FOR SELECT
  TO authenticated
  USING (auth.uid() = created_by);

-- RLS Policies for questions (public read for verified questions)
CREATE POLICY "Anyone can read verified questions"
  ON questions
  FOR SELECT
  TO authenticated
  USING (is_verified = true AND is_active = true);

-- RLS Policies for test_questions (public read)
CREATE POLICY "Anyone can read test questions"
  ON test_questions
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for user_test_attempts
CREATE POLICY "Users can read own test attempts"
  ON user_test_attempts
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own test attempts"
  ON user_test_attempts
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own test attempts"
  ON user_test_attempts
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for user_question_responses
CREATE POLICY "Users can read own question responses"
  ON user_question_responses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = (SELECT user_id FROM user_test_attempts WHERE id = attempt_id));

CREATE POLICY "Users can insert own question responses"
  ON user_question_responses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = (SELECT user_id FROM user_test_attempts WHERE id = attempt_id));

CREATE POLICY "Users can update own question responses"
  ON user_question_responses
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = (SELECT user_id FROM user_test_attempts WHERE id = attempt_id));

-- RLS Policies for question_bookmarks
CREATE POLICY "Users can manage own bookmarks"
  ON question_bookmarks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_tests_type ON tests(test_type);
CREATE INDEX IF NOT EXISTS idx_tests_published ON tests(is_published);
CREATE INDEX IF NOT EXISTS idx_tests_category ON tests(category_id);
CREATE INDEX IF NOT EXISTS idx_questions_subject ON questions(subject_id);
CREATE INDEX IF NOT EXISTS idx_questions_topic ON questions(topic_id);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty);
CREATE INDEX IF NOT EXISTS idx_questions_verified ON questions(is_verified);
CREATE INDEX IF NOT EXISTS idx_test_questions_test_id ON test_questions(test_id);
CREATE INDEX IF NOT EXISTS idx_test_questions_question_id ON test_questions(question_id);
CREATE INDEX IF NOT EXISTS idx_user_test_attempts_user_id ON user_test_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_test_attempts_test_id ON user_test_attempts(test_id);
CREATE INDEX IF NOT EXISTS idx_user_question_responses_attempt_id ON user_question_responses(attempt_id);
CREATE INDEX IF NOT EXISTS idx_user_question_responses_question_id ON user_question_responses(question_id);
CREATE INDEX IF NOT EXISTS idx_question_bookmarks_user_id ON question_bookmarks(user_id);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_tests_updated_at
  BEFORE UPDATE ON tests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_questions_updated_at
  BEFORE UPDATE ON questions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_test_attempts_updated_at
  BEFORE UPDATE ON user_test_attempts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();