import {
  getStudyStats,
  getUserStudySessions,
  getUserAchievements,
  getWeeklyActivity,
  StudySession as DBStudySession,
  Achievement as DBAchievement
} from '@/lib/dataService';

export interface StudySession {
  id: string;
  date: Date;
  subject: string;
  topic: string;
  duration: number; // in minutes
  type: 'study' | 'test' | 'revision';
  score?: number;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  earned: boolean;
  earnedDate?: Date;
  category: 'study' | 'test' | 'streak' | 'milestone';
}

export interface WeeklyActivity {
  day: string;
  date: Date;
  studyHours: number;
  testsCompleted: number;
  topicsCompleted: number;
  xpEarned: number;
}

export const studySessions: StudySession[] = [
  {
    id: 's1',
    date: new Date('2024-01-20'),
    subject: 'Engineering Mathematics',
    topic: 'Linear Algebra',
    duration: 120,
    type: 'study'
  },
  {
    id: 's2',
    date: new Date('2024-01-20'),
    subject: 'Thermodynamics',
    topic: 'First Law',
    duration: 90,
    type: 'study'
  },
  {
    id: 's3',
    date: new Date('2024-01-19'),
    subject: 'Strength of Materials',
    topic: 'Bending',
    duration: 75,
    type: 'test',
    score: 85
  }
];

export const achievements: Achievement[] = [
  {
    id: 'first-steps',
    title: 'First Steps',
    description: 'Completed your first study session',
    icon: 'play',
    earned: true,
    earnedDate: new Date('2024-01-15'),
    category: 'milestone'
  },
  {
    id: 'week-warrior',
    title: 'Week Warrior',
    description: 'Maintained a 7-day study streak',
    icon: 'target',
    earned: true,
    earnedDate: new Date('2024-01-18'),
    category: 'streak'
  },
  {
    id: 'test-master',
    title: 'Test Master',
    description: 'Scored above 90% in 5 tests',
    icon: 'award',
    earned: true,
    earnedDate: new Date('2024-01-19'),
    category: 'test'
  },
  {
    id: 'marathon-runner',
    title: 'Marathon Runner',
    description: 'Study for 30 consecutive days',
    icon: 'zap',
    earned: false,
    category: 'streak'
  },
  {
    id: 'subject-expert',
    title: 'Subject Expert',
    description: 'Complete 100% of any subject',
    icon: 'book',
    earned: false,
    category: 'milestone'
  }
];

export const weeklyActivity: WeeklyActivity[] = [
  {
    day: 'Mon',
    date: new Date('2024-01-15'),
    studyHours: 3.5,
    testsCompleted: 1,
    topicsCompleted: 2,
    xpEarned: 150
  },
  {
    day: 'Tue',
    date: new Date('2024-01-16'),
    studyHours: 2.8,
    testsCompleted: 0,
    topicsCompleted: 1,
    xpEarned: 100
  },
  {
    day: 'Wed',
    date: new Date('2024-01-17'),
    studyHours: 4.2,
    testsCompleted: 2,
    topicsCompleted: 3,
    xpEarned: 220
  },
  {
    day: 'Thu',
    date: new Date('2024-01-18'),
    studyHours: 3.1,
    testsCompleted: 1,
    topicsCompleted: 2,
    xpEarned: 170
  },
  {
    day: 'Fri',
    date: new Date('2024-01-19'),
    studyHours: 2.5,
    testsCompleted: 0,
    topicsCompleted: 1,
    xpEarned: 80
  },
  {
    day: 'Sat',
    date: new Date('2024-01-20'),
    studyHours: 5.0,
    testsCompleted: 3,
    topicsCompleted: 4,
    xpEarned: 300
  },
  {
    day: 'Sun',
    date: new Date('2024-01-21'),
    studyHours: 4.8,
    testsCompleted: 2,
    topicsCompleted: 3,
    xpEarned: 280
  }
];

// Real data functions that fetch from Supabase
export const getProgressStats = async (userId: string) => {
  try {
    // Check if Supabase is configured
    if (!process.env.EXPO_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL === 'https://your-project.supabase.co') {
      // Return demo data for development
      return {
        totalStudyHours: 42,
        totalSessions: 28,
        averageScore: 78,
        currentStreak: 12,
        totalXP: 2450,
        earnedAchievements: 8,
        totalAchievements: 15
      };
    }

    const stats = await getStudyStats(userId);
    const achievements = await getUserAchievements(userId);
    const weeklyData = await getWeeklyActivity(userId);

    const totalXP = weeklyData.reduce((sum, day) => sum + day.xpEarned, 0);
    const earnedAchievements = achievements.filter(achievement => achievement.earned).length;

    return {
      totalStudyHours: stats.totalStudyHours,
      totalSessions: stats.totalSessions,
      averageScore: stats.averageScore,
      currentStreak: stats.currentStreak,
      totalXP,
      earnedAchievements,
      totalAchievements: achievements.length
    };
  } catch (error) {
    console.error('Error fetching progress stats:', error);
    // Return fallback data
    return {
      totalStudyHours: 0,
      totalSessions: 0,
      averageScore: 0,
      currentStreak: 0,
      totalXP: 0,
      earnedAchievements: 0,
      totalAchievements: 0
    };
  }
};

export const getWeeklyStats = async (userId: string) => {
  try {
    const weeklyData = await getWeeklyActivity(userId);

    const totalHours = weeklyData.reduce((sum, day) => sum + day.studyHours, 0);
    const totalTests = weeklyData.reduce((sum, day) => sum + day.testsCompleted, 0);
    const totalTopics = weeklyData.reduce((sum, day) => sum + day.topicsCompleted, 0);
    const totalXP = weeklyData.reduce((sum, day) => sum + day.xpEarned, 0);

    return {
      totalHours: Math.round(totalHours * 10) / 10,
      totalTests,
      totalTopics,
      totalXP,
      averageHoursPerDay: Math.round((totalHours / 7) * 10) / 10
    };
  } catch (error) {
    console.error('Error fetching weekly stats:', error);
    // Return fallback data
    return {
      totalHours: 0,
      totalTests: 0,
      totalTopics: 0,
      totalXP: 0,
      averageHoursPerDay: 0
    };
  }
};

// Helper functions to get real data
export const getStudySessionsData = async (userId: string) => {
  try {
    const sessions = await getUserStudySessions(userId);
    return sessions.map(session => ({
      id: session.id,
      date: new Date(session.date),
      subject: session.subject,
      topic: session.topic,
      duration: session.duration,
      type: session.type as 'study' | 'test' | 'revision',
      score: session.score
    }));
  } catch (error) {
    console.error('Error fetching study sessions:', error);
    return [];
  }
};

export const getAchievementsData = async (userId: string) => {
  try {
    const achievements = await getUserAchievements(userId);
    return achievements.map(achievement => ({
      id: achievement.id,
      title: achievement.title,
      description: achievement.description,
      icon: achievement.icon,
      earned: achievement.earned,
      earnedDate: achievement.earned_date ? new Date(achievement.earned_date) : undefined,
      category: achievement.category as 'study' | 'test' | 'streak' | 'milestone'
    }));
  } catch (error) {
    console.error('Error fetching achievements:', error);
    return [];
  }
};

export const getWeeklyActivityData = async (userId: string) => {
  try {
    return await getWeeklyActivity(userId);
  } catch (error) {
    console.error('Error fetching weekly activity:', error);
    return [];
  }
};