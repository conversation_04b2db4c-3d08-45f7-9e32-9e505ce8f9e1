export interface StudySession {
  id: string;
  date: Date;
  subject: string;
  topic: string;
  duration: number; // in minutes
  type: 'study' | 'test' | 'revision';
  score?: number;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  earned: boolean;
  earnedDate?: Date;
  category: 'study' | 'test' | 'streak' | 'milestone';
}

export interface WeeklyActivity {
  day: string;
  date: Date;
  studyHours: number;
  testsCompleted: number;
  topicsCompleted: number;
  xpEarned: number;
}

export const studySessions: StudySession[] = [
  {
    id: 's1',
    date: new Date('2024-01-20'),
    subject: 'Engineering Mathematics',
    topic: 'Linear Algebra',
    duration: 120,
    type: 'study'
  },
  {
    id: 's2',
    date: new Date('2024-01-20'),
    subject: 'Thermodynamics',
    topic: 'First Law',
    duration: 90,
    type: 'study'
  },
  {
    id: 's3',
    date: new Date('2024-01-19'),
    subject: 'Strength of Materials',
    topic: 'Bending',
    duration: 75,
    type: 'test',
    score: 85
  }
];

export const achievements: Achievement[] = [
  {
    id: 'first-steps',
    title: 'First Steps',
    description: 'Completed your first study session',
    icon: 'play',
    earned: true,
    earnedDate: new Date('2024-01-15'),
    category: 'milestone'
  },
  {
    id: 'week-warrior',
    title: 'Week Warrior',
    description: 'Maintained a 7-day study streak',
    icon: 'target',
    earned: true,
    earnedDate: new Date('2024-01-18'),
    category: 'streak'
  },
  {
    id: 'test-master',
    title: 'Test Master',
    description: 'Scored above 90% in 5 tests',
    icon: 'award',
    earned: true,
    earnedDate: new Date('2024-01-19'),
    category: 'test'
  },
  {
    id: 'marathon-runner',
    title: 'Marathon Runner',
    description: 'Study for 30 consecutive days',
    icon: 'zap',
    earned: false,
    category: 'streak'
  },
  {
    id: 'subject-expert',
    title: 'Subject Expert',
    description: 'Complete 100% of any subject',
    icon: 'book',
    earned: false,
    category: 'milestone'
  }
];

export const weeklyActivity: WeeklyActivity[] = [
  {
    day: 'Mon',
    date: new Date('2024-01-15'),
    studyHours: 3.5,
    testsCompleted: 1,
    topicsCompleted: 2,
    xpEarned: 150
  },
  {
    day: 'Tue',
    date: new Date('2024-01-16'),
    studyHours: 2.8,
    testsCompleted: 0,
    topicsCompleted: 1,
    xpEarned: 100
  },
  {
    day: 'Wed',
    date: new Date('2024-01-17'),
    studyHours: 4.2,
    testsCompleted: 2,
    topicsCompleted: 3,
    xpEarned: 220
  },
  {
    day: 'Thu',
    date: new Date('2024-01-18'),
    studyHours: 3.1,
    testsCompleted: 1,
    topicsCompleted: 2,
    xpEarned: 170
  },
  {
    day: 'Fri',
    date: new Date('2024-01-19'),
    studyHours: 2.5,
    testsCompleted: 0,
    topicsCompleted: 1,
    xpEarned: 80
  },
  {
    day: 'Sat',
    date: new Date('2024-01-20'),
    studyHours: 5.0,
    testsCompleted: 3,
    topicsCompleted: 4,
    xpEarned: 300
  },
  {
    day: 'Sun',
    date: new Date('2024-01-21'),
    studyHours: 4.8,
    testsCompleted: 2,
    topicsCompleted: 3,
    xpEarned: 280
  }
];

export const getProgressStats = () => {
  const totalStudyHours = studySessions.reduce((sum, session) => sum + session.duration, 0) / 60;
  const totalSessions = studySessions.length;
  const testSessions = studySessions.filter(session => session.type === 'test');
  const averageScore = testSessions.length > 0 
    ? Math.round(testSessions.reduce((sum, session) => sum + (session.score || 0), 0) / testSessions.length)
    : 0;
  
  const currentStreak = 12; // This would be calculated based on consecutive study days
  const totalXP = weeklyActivity.reduce((sum, day) => sum + day.xpEarned, 0);
  const earnedAchievements = achievements.filter(achievement => achievement.earned).length;

  return {
    totalStudyHours: Math.round(totalStudyHours),
    totalSessions,
    averageScore,
    currentStreak,
    totalXP,
    earnedAchievements,
    totalAchievements: achievements.length
  };
};

export const getWeeklyStats = () => {
  const totalHours = weeklyActivity.reduce((sum, day) => sum + day.studyHours, 0);
  const totalTests = weeklyActivity.reduce((sum, day) => sum + day.testsCompleted, 0);
  const totalTopics = weeklyActivity.reduce((sum, day) => sum + day.topicsCompleted, 0);
  const totalXP = weeklyActivity.reduce((sum, day) => sum + day.xpEarned, 0);

  return {
    totalHours: Math.round(totalHours * 10) / 10,
    totalTests,
    totalTopics,
    totalXP,
    averageHoursPerDay: Math.round((totalHours / 7) * 10) / 10
  };
};