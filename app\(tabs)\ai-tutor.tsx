import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Bot, Send, Lightbulb, BookOpen, Calculator, Clock, Zap, MessageCircle, Star, ChevronRight } from 'lucide-react-native';

export default function AITutorScreen() {
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState([
    {
      id: 1,
      type: 'ai',
      message: "Hi! I'm your AI tutor powered by Google Gemini. I'm here to help you with GATE Mechanical Engineering preparation. Ask me anything about concepts, formulas, or problem-solving!",
      timestamp: new Date().toLocaleTimeString()
    }
  ]);

  const quickQuestions = [
    {
      id: 1,
      question: "Explain the first law of thermodynamics",
      category: "Thermodynamics",
      icon: Lightbulb,
      color: '#dc2626'
    },
    {
      id: 2,
      question: "What is the difference between stress and strain?",
      category: "Strength of Materials",
      icon: Calculator,
      color: '#059669'
    },
    {
      id: 3,
      question: "Derive the equation for bending moment",
      category: "Mechanics",
      icon: BookOpen,
      color: '#7c3aed'
    },
    {
      id: 4,
      question: "Explain Reynolds number significance",
      category: "Fluid Mechanics",
      icon: Zap,
      color: '#0891b2'
    }
  ];

  const studyPlanSuggestions = [
    {
      title: "Weak Areas Focus",
      description: "Based on your test performance",
      topics: ["Heat Transfer", "Fluid Dynamics", "Machine Design"],
      duration: "2 weeks",
      priority: "High"
    },
    {
      title: "Revision Schedule",
      description: "Quick review of completed topics",
      topics: ["Thermodynamics", "Strength of Materials"],
      duration: "1 week",
      priority: "Medium"
    }
  ];

  const recentQuestions = [
    {
      question: "What is the efficiency of Carnot engine?",
      subject: "Thermodynamics",
      timestamp: "2 hours ago"
    },
    {
      question: "Explain different types of stresses",
      subject: "Strength of Materials",
      timestamp: "Yesterday"
    },
    {
      question: "What is boundary layer theory?",
      subject: "Fluid Mechanics",
      timestamp: "2 days ago"
    }
  ];

  const handleSendMessage = () => {
    if (message.trim()) {
      const newMessage = {
        id: chatHistory.length + 1,
        type: 'user',
        message: message,
        timestamp: new Date().toLocaleTimeString()
      };
      
      setChatHistory([...chatHistory, newMessage]);
      setMessage('');
      
      // Simulate AI response
      setTimeout(() => {
        const aiResponse = {
          id: chatHistory.length + 2,
          type: 'ai',
          message: "I understand your question about " + message + ". Let me explain this concept step by step with relevant formulas and examples...",
          timestamp: new Date().toLocaleTimeString()
        };
        setChatHistory(prev => [...prev, aiResponse]);
      }, 1000);
    }
  };

  const handleQuickQuestion = (question: string) => {
    setMessage(question);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.aiIcon}>
            <Bot size={24} color="#7c3aed" />
          </View>
          <View>
            <Text style={styles.title}>AI Tutor</Text>
            <Text style={styles.subtitle}>Powered by Google Gemini</Text>
          </View>
        </View>
        <View style={styles.statusIndicator}>
          <View style={styles.onlineStatus} />
          <Text style={styles.statusText}>Online</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Questions */}
        <View style={styles.quickQuestionsContainer}>
          <Text style={styles.sectionTitle}>Quick Questions</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickQuestionsScroll}>
            {quickQuestions.map((item) => (
              <TouchableOpacity 
                key={item.id} 
                style={[styles.quickQuestionCard, { backgroundColor: item.color + '15' }]}
                onPress={() => handleQuickQuestion(item.question)}
              >
                <View style={[styles.quickQuestionIcon, { backgroundColor: item.color + '25' }]}>
                  <item.icon size={20} color={item.color} />
                </View>
                <Text style={styles.quickQuestionCategory}>{item.category}</Text>
                <Text style={styles.quickQuestionText}>{item.question}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* AI Study Plans */}
        <View style={styles.studyPlansContainer}>
          <Text style={styles.sectionTitle}>AI Study Plans</Text>
          {studyPlanSuggestions.map((plan, index) => (
            <TouchableOpacity key={index} style={styles.studyPlanCard}>
              <View style={styles.studyPlanHeader}>
                <View>
                  <Text style={styles.studyPlanTitle}>{plan.title}</Text>
                  <Text style={styles.studyPlanDescription}>{plan.description}</Text>
                </View>
                <View style={[styles.priorityBadge, { 
                  backgroundColor: plan.priority === 'High' ? '#fef2f2' : '#fef3c7' 
                }]}>
                  <Text style={[styles.priorityText, { 
                    color: plan.priority === 'High' ? '#dc2626' : '#f59e0b' 
                  }]}>
                    {plan.priority}
                  </Text>
                </View>
              </View>
              <View style={styles.studyPlanDetails}>
                <View style={styles.studyPlanMeta}>
                  <Clock size={16} color="#6b7280" />
                  <Text style={styles.studyPlanDuration}>{plan.duration}</Text>
                </View>
                <View style={styles.topicsContainer}>
                  {plan.topics.map((topic, topicIndex) => (
                    <View key={topicIndex} style={styles.topicChip}>
                      <Text style={styles.topicText}>{topic}</Text>
                    </View>
                  ))}
                </View>
              </View>
              <TouchableOpacity style={styles.generatePlanButton}>
                <Text style={styles.generatePlanText}>Generate Detailed Plan</Text>
                <ChevronRight size={16} color="#7c3aed" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>

        {/* Recent Questions */}
        <View style={styles.recentQuestionsContainer}>
          <Text style={styles.sectionTitle}>Recent Questions</Text>
          <View style={styles.recentQuestionsCard}>
            {recentQuestions.map((item, index) => (
              <TouchableOpacity key={index} style={styles.recentQuestionItem}>
                <View style={styles.recentQuestionContent}>
                  <Text style={styles.recentQuestionText}>{item.question}</Text>
                  <View style={styles.recentQuestionMeta}>
                    <Text style={styles.recentQuestionSubject}>{item.subject}</Text>
                    <Text style={styles.recentQuestionTime}>{item.timestamp}</Text>
                  </View>
                </View>
                <MessageCircle size={16} color="#9ca3af" />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Chat History */}
        <View style={styles.chatContainer}>
          <Text style={styles.sectionTitle}>Chat History</Text>
          <View style={styles.chatHistory}>
            {chatHistory.map((chat) => (
              <View key={chat.id} style={[
                styles.chatMessage,
                chat.type === 'user' ? styles.userMessage : styles.aiMessage
              ]}>
                {chat.type === 'ai' && (
                  <View style={styles.aiAvatar}>
                    <Bot size={16} color="#7c3aed" />
                  </View>
                )}
                <View style={[
                  styles.messageContent,
                  chat.type === 'user' ? styles.userMessageContent : styles.aiMessageContent
                ]}>
                  <Text style={[
                    styles.messageText,
                    chat.type === 'user' ? styles.userMessageText : styles.aiMessageText
                  ]}>
                    {chat.message}
                  </Text>
                  <Text style={styles.messageTime}>{chat.timestamp}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Message Input */}
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.messageInputContainer}>
          <TextInput
            style={styles.messageInput}
            placeholder="Ask me anything about GATE Mechanical..."
            value={message}
            onChangeText={setMessage}
            multiline
            maxLength={500}
          />
          <TouchableOpacity 
            style={[styles.sendButton, { opacity: message.trim() ? 1 : 0.5 }]}
            onPress={handleSendMessage}
            disabled={!message.trim()}
          >
            <Send size={20} color="#ffffff" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#faf5ff',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  onlineStatus: {
    width: 8,
    height: 8,
    backgroundColor: '#059669',
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#059669',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  quickQuestionsContainer: {
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  quickQuestionsScroll: {
    paddingRight: 20,
  },
  quickQuestionCard: {
    width: 200,
    padding: 16,
    borderRadius: 12,
    marginRight: 12,
  },
  quickQuestionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickQuestionCategory: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
    marginBottom: 6,
  },
  quickQuestionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    lineHeight: 18,
  },
  studyPlansContainer: {
    marginBottom: 24,
  },
  studyPlanCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  studyPlanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  studyPlanTitle: {
    fontSize: 16,
    fontFamily: 'RobotoSlab-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  studyPlanDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
  },
  studyPlanDetails: {
    marginBottom: 16,
  },
  studyPlanMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 12,
  },
  studyPlanDuration: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  topicsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  topicChip: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  topicText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
  },
  generatePlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#faf5ff',
    paddingVertical: 12,
    borderRadius: 10,
    gap: 6,
  },
  generatePlanText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#7c3aed',
  },
  recentQuestionsContainer: {
    marginBottom: 24,
  },
  recentQuestionsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recentQuestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  recentQuestionContent: {
    flex: 1,
  },
  recentQuestionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 4,
  },
  recentQuestionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  recentQuestionSubject: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#7c3aed',
  },
  recentQuestionTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
  },
  chatContainer: {
    marginBottom: 100,
  },
  chatHistory: {
    gap: 16,
  },
  chatMessage: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  aiMessage: {
    justifyContent: 'flex-start',
  },
  aiAvatar: {
    width: 32,
    height: 32,
    backgroundColor: '#faf5ff',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  messageContent: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  userMessageContent: {
    backgroundColor: '#1e40af',
    marginLeft: 'auto',
  },
  aiMessageContent: {
    backgroundColor: '#ffffff',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  messageText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    marginBottom: 4,
  },
  userMessageText: {
    color: '#ffffff',
  },
  aiMessageText: {
    color: '#111827',
  },
  messageTime: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
  },
  inputContainer: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  messageInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  messageInput: {
    flex: 1,
    backgroundColor: '#f9fafb',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    maxHeight: 100,
  },
  sendButton: {
    width: 44,
    height: 44,
    backgroundColor: '#1e40af',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
});