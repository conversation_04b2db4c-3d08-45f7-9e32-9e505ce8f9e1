/*
  # Content and Learning Resources Schema
  
  1. Tables Created
    - content_categories: Categories for organizing learning content
    - learning_content: Videos, articles, PDFs, and other resources
    - content_progress: User progress through content
    - video_sessions: Detailed video watching sessions
    - bookmarks: User bookmarked content
    - content_ratings: User ratings and reviews for content
    - playlists: User-created content playlists
    - content_downloads: Track downloaded content for offline use
    
  2. Features
    - Comprehensive content management system
    - Video progress tracking with resume functionality
    - Content rating and review system
    - Playlist creation and management
    - Bookmark system for quick access
    - Offline content download tracking
*/

-- Content categories for organization
CREATE TABLE IF NOT EXISTS content_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  parent_category_id uuid REFERENCES content_categories(id),
  icon text DEFAULT 'folder',
  color text DEFAULT '#6b7280',
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Learning content (videos, articles, PDFs, etc.)
CREATE TABLE IF NOT EXISTS learning_content (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  content_type text NOT NULL CHECK (content_type IN ('video', 'article', 'pdf', 'quiz', 'interactive', 'audio')),
  category_id uuid REFERENCES content_categories(id),
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  subtopic_id uuid REFERENCES subtopics(id),
  content_url text NOT NULL,
  thumbnail_url text,
  duration_seconds integer, -- For videos and audio
  file_size_mb decimal(8,2),
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  language text DEFAULT 'English',
  author_name text,
  author_credentials text,
  source_platform text, -- YouTube, Khan Academy, etc.
  external_id text, -- ID on external platform
  tags text[] DEFAULT '{}',
  prerequisites text[] DEFAULT '{}',
  learning_objectives text[] DEFAULT '{}',
  transcript text,
  closed_captions_url text,
  is_free boolean DEFAULT true,
  is_premium boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  is_active boolean DEFAULT true,
  view_count integer DEFAULT 0,
  like_count integer DEFAULT 0,
  average_rating decimal(3,2) DEFAULT 0,
  total_ratings integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  published_at timestamptz
);

-- User progress through content
CREATE TABLE IF NOT EXISTS content_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id) ON DELETE CASCADE,
  progress_percentage integer DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  last_position_seconds integer DEFAULT 0, -- For videos/audio
  is_completed boolean DEFAULT false,
  completion_date timestamptz,
  total_time_spent_seconds integer DEFAULT 0,
  session_count integer DEFAULT 0,
  last_accessed_at timestamptz DEFAULT now(),
  notes text,
  bookmarked_positions jsonb DEFAULT '[]', -- Specific timestamps bookmarked
  playback_speed decimal(3,2) DEFAULT 1.0,
  quality_preference text DEFAULT 'auto',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, content_id)
);

-- Detailed video watching sessions
CREATE TABLE IF NOT EXISTS video_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id) ON DELETE CASCADE,
  session_start_time timestamptz DEFAULT now(),
  session_end_time timestamptz,
  start_position_seconds integer DEFAULT 0,
  end_position_seconds integer DEFAULT 0,
  total_watch_time_seconds integer DEFAULT 0,
  playback_speed decimal(3,2) DEFAULT 1.0,
  quality_watched text DEFAULT 'auto',
  device_type text DEFAULT 'mobile',
  network_type text, -- wifi, cellular, etc.
  pauses_count integer DEFAULT 0,
  seeks_count integer DEFAULT 0,
  fullscreen_time_seconds integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- User bookmarks for content
CREATE TABLE IF NOT EXISTS bookmarks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id) ON DELETE CASCADE,
  bookmark_type text DEFAULT 'content' CHECK (bookmark_type IN ('content', 'timestamp', 'note')),
  position_seconds integer, -- For video/audio bookmarks
  title text,
  notes text,
  tags text[] DEFAULT '{}',
  is_private boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Content ratings and reviews
CREATE TABLE IF NOT EXISTS content_ratings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id) ON DELETE CASCADE,
  rating integer NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text text,
  review_title text,
  helpful_count integer DEFAULT 0,
  is_verified_review boolean DEFAULT false,
  pros text[] DEFAULT '{}',
  cons text[] DEFAULT '{}',
  would_recommend boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, content_id)
);

-- User-created playlists
CREATE TABLE IF NOT EXISTS playlists (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  is_public boolean DEFAULT false,
  is_collaborative boolean DEFAULT false,
  thumbnail_url text,
  total_duration_seconds integer DEFAULT 0,
  content_count integer DEFAULT 0,
  tags text[] DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Playlist content items
CREATE TABLE IF NOT EXISTS playlist_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  playlist_id uuid REFERENCES playlists(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id) ON DELETE CASCADE,
  display_order integer NOT NULL,
  added_by uuid REFERENCES users(id),
  notes text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(playlist_id, content_id),
  UNIQUE(playlist_id, display_order)
);

-- Content downloads for offline use
CREATE TABLE IF NOT EXISTS content_downloads (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content_id uuid REFERENCES learning_content(id) ON DELETE CASCADE,
  download_quality text DEFAULT 'medium',
  file_size_mb decimal(8,2),
  download_started_at timestamptz DEFAULT now(),
  download_completed_at timestamptz,
  download_status text DEFAULT 'pending' CHECK (download_status IN ('pending', 'downloading', 'completed', 'failed', 'expired')),
  download_progress integer DEFAULT 0 CHECK (download_progress >= 0 AND download_progress <= 100),
  expires_at timestamptz,
  local_file_path text,
  error_message text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, content_id)
);

-- Enable Row Level Security
ALTER TABLE content_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_downloads ENABLE ROW LEVEL SECURITY;

-- RLS Policies for content_categories (public read)
CREATE POLICY "Anyone can read content categories"
  ON content_categories
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for learning_content (public read for active content)
CREATE POLICY "Anyone can read active content"
  ON learning_content
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- RLS Policies for content_progress
CREATE POLICY "Users can manage own content progress"
  ON content_progress
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for video_sessions
CREATE POLICY "Users can manage own video sessions"
  ON video_sessions
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for bookmarks
CREATE POLICY "Users can manage own bookmarks"
  ON bookmarks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for content_ratings
CREATE POLICY "Users can manage own content ratings"
  ON content_ratings
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Anyone can read content ratings"
  ON content_ratings
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for playlists
CREATE POLICY "Users can manage own playlists"
  ON playlists
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Anyone can read public playlists"
  ON playlists
  FOR SELECT
  TO authenticated
  USING (is_public = true);

-- RLS Policies for playlist_items
CREATE POLICY "Users can manage own playlist items"
  ON playlist_items
  FOR ALL
  TO authenticated
  USING (auth.uid() = (SELECT user_id FROM playlists WHERE id = playlist_id))
  WITH CHECK (auth.uid() = (SELECT user_id FROM playlists WHERE id = playlist_id));

CREATE POLICY "Anyone can read public playlist items"
  ON playlist_items
  FOR SELECT
  TO authenticated
  USING ((SELECT is_public FROM playlists WHERE id = playlist_id) = true);

-- RLS Policies for content_downloads
CREATE POLICY "Users can manage own content downloads"
  ON content_downloads
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_content_categories_parent ON content_categories(parent_category_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_category ON learning_content(category_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_subject ON learning_content(subject_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_topic ON learning_content(topic_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_learning_content_active ON learning_content(is_active);
CREATE INDEX IF NOT EXISTS idx_content_progress_user_id ON content_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_content_progress_content_id ON content_progress(content_id);
CREATE INDEX IF NOT EXISTS idx_video_sessions_user_id ON video_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_video_sessions_content_id ON video_sessions(content_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_id ON bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_content_id ON bookmarks(content_id);
CREATE INDEX IF NOT EXISTS idx_content_ratings_content_id ON content_ratings(content_id);
CREATE INDEX IF NOT EXISTS idx_playlists_user_id ON playlists(user_id);
CREATE INDEX IF NOT EXISTS idx_playlists_public ON playlists(is_public);
CREATE INDEX IF NOT EXISTS idx_playlist_items_playlist_id ON playlist_items(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_items_content_id ON playlist_items(content_id);
CREATE INDEX IF NOT EXISTS idx_content_downloads_user_id ON content_downloads(user_id);
CREATE INDEX IF NOT EXISTS idx_content_downloads_status ON content_downloads(download_status);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_learning_content_updated_at
  BEFORE UPDATE ON learning_content
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_progress_updated_at
  BEFORE UPDATE ON content_progress
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookmarks_updated_at
  BEFORE UPDATE ON bookmarks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_ratings_updated_at
  BEFORE UPDATE ON content_ratings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_playlists_updated_at
  BEFORE UPDATE ON playlists
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_downloads_updated_at
  BEFORE UPDATE ON content_downloads
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();