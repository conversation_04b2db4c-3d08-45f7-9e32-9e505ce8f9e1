export interface Subtopic {
  id: string;
  name: string;
  description: string;
  estimatedHours: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  completed: boolean;
  videoUrl?: string;
  notesUrl?: string;
}

export interface Topic {
  id: string;
  name: string;
  description: string;
  subtopics: Subtopic[];
  estimatedHours: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  completed: boolean;
}

export interface Subject {
  id: string;
  name: string;
  section: string;
  description: string;
  topics: Topic[];
  color: string;
  bgColor: string;
  estimatedHours: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  completed: number;
  total: number;
}

export const gateSubjects: Subject[] = [
  // Section 1: Engineering Mathematics
  {
    id: 'eng-math',
    name: 'Engineering Mathematics',
    section: 'Section 1: Engineering Mathematics',
    description: 'Foundation mathematics for engineering applications including linear algebra, calculus, differential equations, complex variables, probability and statistics, and numerical methods.',
    color: '#1e40af',
    bgColor: '#eff6ff',
    estimatedHours: 120,
    difficulty: 'Hard',
    completed: 15,
    total: 25,
    topics: [
      {
        id: 'linear-algebra',
        name: 'Linear Algebra',
        description: 'Matrix algebra, systems of linear equations, eigenvalues and eigenvectors',
        estimatedHours: 20,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'matrix-algebra',
            name: 'Matrix Algebra',
            description: 'Matrix operations, determinants, and inverse matrices',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true,
            videoUrl: 'https://example.com/matrix-algebra',
            notesUrl: 'https://example.com/matrix-notes'
          },
          {
            id: 'linear-equations',
            name: 'Systems of Linear Equations',
            description: 'Solving systems using Gaussian elimination and matrix methods',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'eigenvalues',
            name: 'Eigenvalues and Eigenvectors',
            description: 'Characteristic equations, diagonalization, and applications',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'calculus',
        name: 'Calculus',
        description: 'Single and multivariable calculus, limits, derivatives, integrals',
        estimatedHours: 35,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'single-variable',
            name: 'Functions of Single Variable',
            description: 'Limits, continuity, differentiability, mean value theorems',
            estimatedHours: 12,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'integrals',
            name: 'Integration',
            description: 'Definite, improper, double and triple integrals',
            estimatedHours: 12,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'multivariable',
            name: 'Multivariable Calculus',
            description: 'Partial derivatives, Taylor series, maxima and minima',
            estimatedHours: 11,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'differential-equations',
        name: 'Differential Equations',
        description: 'First and higher order differential equations, Laplace transforms',
        estimatedHours: 25,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'first-order',
            name: 'First Order Equations',
            description: 'Linear and nonlinear first order differential equations',
            estimatedHours: 10,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'higher-order',
            name: 'Higher Order Linear Equations',
            description: 'Constant coefficients and Euler-Cauchy equations',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'laplace-transforms',
            name: 'Laplace Transforms',
            description: 'Transform methods for solving differential equations',
            estimatedHours: 7,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'complex-variables',
        name: 'Complex Variables',
        description: 'Analytic functions, Cauchy-Riemann equations, complex integration',
        estimatedHours: 15,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'analytic-functions',
            name: 'Analytic Functions',
            description: 'Complex functions and their properties',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'cauchy-riemann',
            name: 'Cauchy-Riemann Equations',
            description: 'Conditions for analyticity',
            estimatedHours: 4,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'complex-integration',
            name: 'Complex Integration',
            description: 'Cauchy\'s theorem and integral formula, Taylor and Laurent series',
            estimatedHours: 5,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'probability-statistics',
        name: 'Probability and Statistics',
        description: 'Probability theory, distributions, sampling, and statistical analysis',
        estimatedHours: 15,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'probability-basics',
            name: 'Probability Fundamentals',
            description: 'Definitions, conditional probability, sampling theorems',
            estimatedHours: 6,
            difficulty: 'Easy',
            completed: false
          },
          {
            id: 'distributions',
            name: 'Probability Distributions',
            description: 'Binomial, Poisson, and normal distributions',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'statistics',
            name: 'Statistics',
            description: 'Mean, median, mode, standard deviation, random variables',
            estimatedHours: 3,
            difficulty: 'Easy',
            completed: false
          }
        ]
      },
      {
        id: 'numerical-methods',
        name: 'Numerical Methods',
        description: 'Numerical solutions for algebraic equations, integration, and differential equations',
        estimatedHours: 10,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'algebraic-equations',
            name: 'Numerical Solutions of Equations',
            description: 'Linear and non-linear algebraic equations',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'numerical-integration',
            name: 'Numerical Integration',
            description: 'Trapezoidal and Simpson\'s rules',
            estimatedHours: 3,
            difficulty: 'Easy',
            completed: false
          },
          {
            id: 'differential-methods',
            name: 'Differential Equation Methods',
            description: 'Single and multi-step methods for differential equations',
            estimatedHours: 3,
            difficulty: 'Medium',
            completed: false
          }
        ]
      }
    ]
  },

  // Section 2: Applied Mechanics and Design
  {
    id: 'eng-mechanics',
    name: 'Engineering Mechanics',
    section: 'Section 2: Applied Mechanics and Design',
    description: 'Free body diagrams, equilibrium, friction, trusses, kinematics, dynamics, and Lagrange\'s equations',
    color: '#059669',
    bgColor: '#ecfdf5',
    estimatedHours: 60,
    difficulty: 'Medium',
    completed: 18,
    total: 22,
    topics: [
      {
        id: 'statics',
        name: 'Statics',
        description: 'Free body diagrams, equilibrium, friction applications',
        estimatedHours: 20,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'free-body-diagrams',
            name: 'Free Body Diagrams and Equilibrium',
            description: 'Drawing and analyzing free body diagrams for equilibrium',
            estimatedHours: 6,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'friction',
            name: 'Friction Applications',
            description: 'Rolling friction, belt-pulley, brakes, clutches, screw jack, wedge',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'trusses-frames',
            name: 'Trusses and Frames',
            description: 'Analysis of statically determinate structures',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          }
        ]
      },
      {
        id: 'dynamics',
        name: 'Dynamics',
        description: 'Kinematics and dynamics of rigid bodies, virtual work',
        estimatedHours: 25,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'kinematics',
            name: 'Kinematics of Rigid Bodies',
            description: 'Plane motion analysis of rigid bodies',
            estimatedHours: 10,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'kinetics',
            name: 'Dynamics of Rigid Bodies',
            description: 'Force and motion relationships in plane motion',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'energy-momentum',
            name: 'Energy and Momentum',
            description: 'Impulse, momentum (linear and angular), energy formulations',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'virtual-work',
        name: 'Virtual Work and Lagrange\'s Equations',
        description: 'Principle of virtual work and Lagrangian mechanics',
        estimatedHours: 15,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'virtual-work-principle',
            name: 'Virtual Work Principle',
            description: 'Application of virtual work in static and dynamic systems',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'lagrange-equations',
            name: 'Lagrange\'s Equations',
            description: 'Lagrangian formulation of dynamics',
            estimatedHours: 7,
            difficulty: 'Hard',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'strength-materials',
    name: 'Mechanics of Materials',
    section: 'Section 2: Applied Mechanics and Design',
    description: 'Stress, strain, elastic constants, bending, torsion, columns, energy methods, thermal stresses, and material testing',
    color: '#7c3aed',
    bgColor: '#faf5ff',
    estimatedHours: 80,
    difficulty: 'Hard',
    completed: 12,
    total: 28,
    topics: [
      {
        id: 'stress-strain',
        name: 'Stress and Strain',
        description: 'Basic concepts, elastic constants, Mohr\'s circle',
        estimatedHours: 18,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'stress-strain-basics',
            name: 'Stress and Strain Fundamentals',
            description: 'Normal and shear stress, strain definitions',
            estimatedHours: 6,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'elastic-constants',
            name: 'Elastic Constants',
            description: 'Young\'s modulus, Poisson\'s ratio, relationships',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'mohrs-circle',
            name: 'Mohr\'s Circle',
            description: 'Plane stress and plane strain analysis',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'pressure-vessels',
        name: 'Thin Cylinders',
        description: 'Stress analysis in thin-walled pressure vessels',
        estimatedHours: 8,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'thin-cylinders',
            name: 'Thin Cylindrical Vessels',
            description: 'Hoop and longitudinal stresses in thin cylinders',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'bending',
        name: 'Bending of Beams',
        description: 'Shear force, bending moment, bending stress, deflection',
        estimatedHours: 25,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'shear-bending',
            name: 'Shear Force and Bending Moment',
            description: 'SFD and BMD for various loading conditions',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'bending-stress',
            name: 'Bending and Shear Stresses',
            description: 'Flexural stress distribution, shear center concept',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'beam-deflection',
            name: 'Deflection of Beams',
            description: 'Methods for calculating beam deflections',
            estimatedHours: 7,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'torsion',
        name: 'Torsion of Circular Shafts',
        description: 'Twisting of circular shafts, shear stress distribution',
        estimatedHours: 12,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'circular-shafts',
            name: 'Torsion of Circular Shafts',
            description: 'Shear stress and angle of twist in circular shafts',
            estimatedHours: 12,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'columns',
        name: 'Euler\'s Theory of Columns',
        description: 'Buckling analysis of long columns',
        estimatedHours: 10,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'euler-columns',
            name: 'Euler\'s Column Theory',
            description: 'Critical load and buckling analysis',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'energy-methods',
        name: 'Energy Methods',
        description: 'Strain energy and energy methods in structural analysis',
        estimatedHours: 7,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'strain-energy',
            name: 'Strain Energy Methods',
            description: 'Castigliano\'s theorem and energy principles',
            estimatedHours: 7,
            difficulty: 'Hard',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'theory-machines',
    name: 'Theory of Machines',
    section: 'Section 2: Applied Mechanics and Design',
    description: 'Kinematics and dynamics of mechanisms, cams, gears, flywheels, governors, balancing, and gyroscopes',
    color: '#dc2626',
    bgColor: '#fef2f2',
    estimatedHours: 70,
    difficulty: 'Hard',
    completed: 8,
    total: 24,
    topics: [
      {
        id: 'mechanisms',
        name: 'Plane Mechanisms',
        description: 'Displacement, velocity, and acceleration analysis',
        estimatedHours: 20,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'kinematic-analysis',
            name: 'Kinematic Analysis',
            description: 'Displacement, velocity, and acceleration analysis of plane mechanisms',
            estimatedHours: 12,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'dynamic-analysis',
            name: 'Dynamic Analysis of Linkages',
            description: 'Force analysis in mechanisms',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'cams',
        name: 'Cams',
        description: 'Cam profile design and analysis',
        estimatedHours: 12,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'cam-design',
            name: 'Cam Design and Analysis',
            description: 'Cam profile generation and motion analysis',
            estimatedHours: 12,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'gears',
        name: 'Gears and Gear Trains',
        description: 'Gear geometry, gear trains, and power transmission',
        estimatedHours: 18,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'gear-geometry',
            name: 'Gear Geometry',
            description: 'Involute gear tooth profiles and geometry',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'gear-trains',
            name: 'Gear Trains',
            description: 'Simple, compound, and epicyclic gear trains',
            estimatedHours: 10,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'flywheels-governors',
        name: 'Flywheels and Governors',
        description: 'Energy storage and speed control mechanisms',
        estimatedHours: 12,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'flywheels',
            name: 'Flywheels',
            description: 'Energy storage and fluctuation control',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'governors',
            name: 'Governors',
            description: 'Speed control mechanisms',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'balancing',
        name: 'Balancing',
        description: 'Balancing of rotating and reciprocating masses',
        estimatedHours: 8,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'rotating-masses',
            name: 'Balancing of Rotating Masses',
            description: 'Static and dynamic balancing',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'reciprocating-masses',
            name: 'Balancing of Reciprocating Masses',
            description: 'Primary and secondary balancing',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'vibrations',
    name: 'Vibrations',
    section: 'Section 2: Applied Mechanics and Design',
    description: 'Free and forced vibrations, damping, resonance, and critical speeds',
    color: '#0891b2',
    bgColor: '#ecfeff',
    estimatedHours: 35,
    difficulty: 'Hard',
    completed: 6,
    total: 12,
    topics: [
      {
        id: 'single-dof',
        name: 'Single Degree of Freedom Systems',
        description: 'Free and forced vibrations of SDOF systems',
        estimatedHours: 20,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'free-vibration',
            name: 'Free Vibration',
            description: 'Undamped and damped free vibrations',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'forced-vibration',
            name: 'Forced Vibration',
            description: 'Response to harmonic and arbitrary excitation',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'damping-effects',
            name: 'Effect of Damping',
            description: 'Viscous, Coulomb, and structural damping',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'vibration-control',
        name: 'Vibration Isolation and Resonance',
        description: 'Vibration isolation techniques and resonance phenomena',
        estimatedHours: 10,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'vibration-isolation',
            name: 'Vibration Isolation',
            description: 'Isolation techniques and transmissibility',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'resonance',
            name: 'Resonance',
            description: 'Resonance conditions and avoidance',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'critical-speeds',
        name: 'Critical Speeds of Shafts',
        description: 'Whirling of shafts and critical speed calculation',
        estimatedHours: 5,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'shaft-whirling',
            name: 'Critical Speeds',
            description: 'Whirling of shafts and critical speed analysis',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'machine-design',
    name: 'Machine Design',
    section: 'Section 2: Applied Mechanics and Design',
    description: 'Design for static and dynamic loading, failure theories, fatigue, and machine elements design',
    color: '#be185d',
    bgColor: '#fdf2f8',
    estimatedHours: 90,
    difficulty: 'Hard',
    completed: 14,
    total: 32,
    topics: [
      {
        id: 'design-fundamentals',
        name: 'Design Fundamentals',
        description: 'Static and dynamic loading, failure theories, fatigue',
        estimatedHours: 25,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'static-loading',
            name: 'Design for Static Loading',
            description: 'Static failure theories and design criteria',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'dynamic-loading',
            name: 'Design for Dynamic Loading',
            description: 'Dynamic loading and impact considerations',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'failure-theories',
            name: 'Failure Theories',
            description: 'Maximum stress, strain, and distortion energy theories',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'fatigue',
            name: 'Fatigue Strength and S-N Diagram',
            description: 'Fatigue analysis and endurance limit',
            estimatedHours: 3,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'joints',
        name: 'Joints',
        description: 'Bolted, riveted, and welded joints design',
        estimatedHours: 20,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'bolted-joints',
            name: 'Bolted Joints',
            description: 'Design of bolted connections',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'riveted-joints',
            name: 'Riveted Joints',
            description: 'Design of riveted connections',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'welded-joints',
            name: 'Welded Joints',
            description: 'Design of welded connections',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'shafts',
        name: 'Shafts',
        description: 'Design of transmission shafts',
        estimatedHours: 12,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'shaft-design',
            name: 'Shaft Design',
            description: 'Design of shafts for torsion and bending',
            estimatedHours: 12,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'bearings',
        name: 'Bearings',
        description: 'Rolling and sliding contact bearings',
        estimatedHours: 15,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'rolling-bearings',
            name: 'Rolling Contact Bearings',
            description: 'Selection and design of ball and roller bearings',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'sliding-bearings',
            name: 'Sliding Contact Bearings',
            description: 'Design of journal bearings',
            estimatedHours: 7,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'power-transmission',
        name: 'Power Transmission Elements',
        description: 'Gears, brakes, clutches, and springs',
        estimatedHours: 18,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'gear-design',
            name: 'Gear Design',
            description: 'Design of spur, helical, and bevel gears',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'brakes-clutches',
            name: 'Brakes and Clutches',
            description: 'Design of friction brakes and clutches',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'springs',
            name: 'Springs',
            description: 'Design of helical and leaf springs',
            estimatedHours: 4,
            difficulty: 'Easy',
            completed: false
          }
        ]
      }
    ]
  },

  // Section 3: Fluid Mechanics and Thermal Sciences
  {
    id: 'fluid-mechanics',
    name: 'Fluid Mechanics',
    section: 'Section 3: Fluid Mechanics and Thermal Sciences',
    description: 'Fluid properties, statics, dynamics, dimensional analysis, viscous flow, boundary layer, and compressible flow',
    color: '#0891b2',
    bgColor: '#ecfeff',
    estimatedHours: 85,
    difficulty: 'Hard',
    completed: 16,
    total: 30,
    topics: [
      {
        id: 'fluid-properties',
        name: 'Fluid Properties',
        description: 'Physical properties of fluids',
        estimatedHours: 8,
        difficulty: 'Easy',
        completed: true,
        subtopics: [
          {
            id: 'basic-properties',
            name: 'Basic Fluid Properties',
            description: 'Density, specific gravity, viscosity, surface tension',
            estimatedHours: 8,
            difficulty: 'Easy',
            completed: true
          }
        ]
      },
      {
        id: 'fluid-statics',
        name: 'Fluid Statics',
        description: 'Forces on submerged bodies, stability of floating bodies',
        estimatedHours: 15,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'hydrostatic-pressure',
            name: 'Hydrostatic Pressure',
            description: 'Pressure variation in static fluids',
            estimatedHours: 5,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'submerged-surfaces',
            name: 'Forces on Submerged Bodies',
            description: 'Hydrostatic forces on plane and curved surfaces',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'buoyancy-stability',
            name: 'Stability of Floating Bodies',
            description: 'Buoyancy, metacentric height, and stability',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'fluid-dynamics',
        name: 'Fluid Dynamics',
        description: 'Control volume analysis, Bernoulli\'s equation, dimensional analysis',
        estimatedHours: 25,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'control-volume',
            name: 'Control Volume Analysis',
            description: 'Mass, momentum, and energy analysis',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'fluid-acceleration',
            name: 'Fluid Acceleration',
            description: 'Local and convective acceleration',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'continuity-momentum',
            name: 'Continuity and Momentum Equations',
            description: 'Differential equations of continuity and momentum',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'bernoulli-equation',
            name: 'Bernoulli\'s Equation',
            description: 'Energy equation and applications',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'dimensional-analysis',
            name: 'Dimensional Analysis',
            description: 'Buckingham Pi theorem and similarity',
            estimatedHours: 3,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'viscous-flow',
        name: 'Viscous Flow',
        description: 'Boundary layer, turbulent flow, pipe flow',
        estimatedHours: 22,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'boundary-layer',
            name: 'Boundary Layer',
            description: 'Boundary layer theory and characteristics',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'turbulent-flow',
            name: 'Elementary Turbulent Flow',
            description: 'Turbulence characteristics and modeling',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'pipe-flow',
            name: 'Flow Through Pipes',
            description: 'Laminar and turbulent pipe flow, head losses',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'head-losses',
        name: 'Head Losses',
        description: 'Major and minor losses in pipes, bends, and fittings',
        estimatedHours: 10,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'pipe-losses',
            name: 'Head Losses in Pipes, Bends and Fittings',
            description: 'Friction losses and minor losses',
            estimatedHours: 10,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'compressible-flow',
        name: 'Compressible Fluid Flow',
        description: 'Basics of compressible flow',
        estimatedHours: 5,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'compressible-basics',
            name: 'Basics of Compressible Fluid Flow',
            description: 'Mach number, shock waves, and isentropic flow',
            estimatedHours: 5,
            difficulty: 'Hard',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'heat-transfer',
    name: 'Heat Transfer',
    section: 'Section 3: Fluid Mechanics and Thermal Sciences',
    description: 'Conduction, convection, radiation, heat exchangers, and thermal boundary layer',
    color: '#be185d',
    bgColor: '#fdf2f8',
    estimatedHours: 75,
    difficulty: 'Hard',
    completed: 12,
    total: 26,
    topics: [
      {
        id: 'conduction',
        name: 'Heat Conduction',
        description: 'One-dimensional conduction, fins, unsteady conduction',
        estimatedHours: 25,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'steady-conduction',
            name: 'One Dimensional Heat Conduction',
            description: 'Steady-state conduction, resistance concept, electrical analogy',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'fins',
            name: 'Heat Transfer Through Fins',
            description: 'Extended surface heat transfer',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'unsteady-conduction',
            name: 'Unsteady Heat Conduction',
            description: 'Lumped parameter system, Heisler\'s charts',
            estimatedHours: 11,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'convection',
        name: 'Heat Convection',
        description: 'Thermal boundary layer, free and forced convection',
        estimatedHours: 30,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'thermal-boundary-layer',
            name: 'Thermal Boundary Layer',
            description: 'Boundary layer development and characteristics',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'dimensionless-parameters',
            name: 'Dimensionless Parameters',
            description: 'Nusselt, Prandtl, Reynolds, and Grashof numbers',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'forced-convection',
            name: 'Forced Convective Heat Transfer',
            description: 'Heat transfer correlations for flow over flat plates and through pipes',
            estimatedHours: 12,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'free-convection',
            name: 'Free Convective Heat Transfer',
            description: 'Natural convection heat transfer',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'turbulence-effect',
            name: 'Effect of Turbulence',
            description: 'Turbulence effects on heat transfer',
            estimatedHours: 2,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'heat-exchangers',
        name: 'Heat Exchangers',
        description: 'Heat exchanger performance, LMTD and NTU methods',
        estimatedHours: 12,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'heat-exchanger-performance',
            name: 'Heat Exchanger Performance',
            description: 'Types and performance analysis',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'lmtd-method',
            name: 'LMTD Method',
            description: 'Log mean temperature difference method',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'ntu-method',
            name: 'NTU Method',
            description: 'Number of transfer units method',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'radiation',
        name: 'Radiative Heat Transfer',
        description: 'Stefan-Boltzmann law, view factors, radiation network analysis',
        estimatedHours: 8,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'radiation-laws',
            name: 'Stefan-Boltzmann Law, Wien\'s Displacement Law',
            description: 'Fundamental radiation laws',
            estimatedHours: 2,
            difficulty: 'Easy',
            completed: false
          },
          {
            id: 'surfaces',
            name: 'Black and Grey Surfaces',
            description: 'Surface properties and emissivity',
            estimatedHours: 2,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'view-factors',
            name: 'View Factors',
            description: 'Geometric factors in radiation exchange',
            estimatedHours: 2,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'radiation-network',
            name: 'Radiation Network Analysis',
            description: 'Network method for radiation problems',
            estimatedHours: 2,
            difficulty: 'Hard',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'thermodynamics',
    name: 'Thermodynamics',
    section: 'Section 3: Fluid Mechanics and Thermal Sciences',
    description: 'Thermodynamic systems, properties, laws, cycles, and thermodynamic relations',
    color: '#dc2626',
    bgColor: '#fef2f2',
    estimatedHours: 80,
    difficulty: 'Hard',
    completed: 18,
    total: 28,
    topics: [
      {
        id: 'basic-concepts',
        name: 'Thermodynamic Systems and Processes',
        description: 'Systems, processes, and properties',
        estimatedHours: 12,
        difficulty: 'Easy',
        completed: true,
        subtopics: [
          {
            id: 'systems-processes',
            name: 'Thermodynamic Systems and Processes',
            description: 'Types of systems and processes',
            estimatedHours: 6,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'pure-substances',
            name: 'Properties of Pure Substances',
            description: 'State postulate and property relations',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: true
          }
        ]
      },
      {
        id: 'gas-behavior',
        name: 'Behavior of Ideal and Real Gases',
        description: 'Gas laws and equations of state',
        estimatedHours: 10,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'ideal-gas',
            name: 'Ideal Gas Behavior',
            description: 'Ideal gas law and processes',
            estimatedHours: 5,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'real-gas',
            name: 'Real Gas Behavior',
            description: 'Compressibility factor and equations of state',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'zeroth-first-law',
        name: 'Zeroth and First Laws of Thermodynamics',
        description: 'Temperature concept and energy conservation',
        estimatedHours: 18,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'zeroth-law',
            name: 'Zeroth Law of Thermodynamics',
            description: 'Temperature and thermal equilibrium',
            estimatedHours: 3,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'first-law',
            name: 'First Law of Thermodynamics',
            description: 'Energy conservation principle',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'work-heat',
            name: 'Calculation of Work and Heat',
            description: 'Work and heat in various processes',
            estimatedHours: 7,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'second-law',
        name: 'Second Law of Thermodynamics',
        description: 'Entropy, irreversibility, and availability',
        estimatedHours: 20,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'second-law-statements',
            name: 'Second Law of Thermodynamics',
            description: 'Kelvin-Planck and Clausius statements',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'entropy',
            name: 'Entropy',
            description: 'Entropy principle and calculations',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'availability',
            name: 'Availability and Irreversibility',
            description: 'Exergy analysis and irreversibility',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'property-charts',
        name: 'Thermodynamic Property Charts and Tables',
        description: 'Steam tables, Mollier diagram, and property relations',
        estimatedHours: 8,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'property-tables',
            name: 'Thermodynamic Property Charts and Tables',
            description: 'Steam tables, refrigerant tables, and charts',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'thermodynamic-relations',
        name: 'Thermodynamic Relations',
        description: 'Maxwell relations and property relationships',
        estimatedHours: 12,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'maxwell-relations',
            name: 'Thermodynamic Relations',
            description: 'Maxwell relations and property derivatives',
            estimatedHours: 12,
            difficulty: 'Hard',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'applications',
    name: 'Applications',
    section: 'Section 3: Fluid Mechanics and Thermal Sciences',
    description: 'Power engineering, IC engines, refrigeration, air conditioning, and turbomachinery',
    color: '#f59e0b',
    bgColor: '#fefbf2',
    estimatedHours: 100,
    difficulty: 'Hard',
    completed: 8,
    total: 35,
    topics: [
      {
        id: 'power-engineering',
        name: 'Power Engineering',
        description: 'Air and gas compressors, vapor and gas power cycles',
        estimatedHours: 30,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'compressors',
            name: 'Air and Gas Compressors',
            description: 'Reciprocating and rotary compressors',
            estimatedHours: 10,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'vapor-cycles',
            name: 'Vapour Power Cycles',
            description: 'Rankine cycle and modifications',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'gas-cycles',
            name: 'Gas Power Cycles',
            description: 'Brayton cycle and gas turbine cycles',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'regeneration-reheat',
            name: 'Concepts of Regeneration and Reheat',
            description: 'Cycle improvements and efficiency enhancement',
            estimatedHours: 4,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'ic-engines',
        name: 'I.C. Engines',
        description: 'Air-standard Otto, Diesel, and dual cycles',
        estimatedHours: 20,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'otto-cycle',
            name: 'Air-standard Otto Cycle',
            description: 'Spark ignition engine cycle',
            estimatedHours: 7,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'diesel-cycle',
            name: 'Air-standard Diesel Cycle',
            description: 'Compression ignition engine cycle',
            estimatedHours: 7,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'dual-cycle',
            name: 'Air-standard Dual Cycle',
            description: 'Combined Otto-Diesel cycle',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'refrigeration',
        name: 'Refrigeration and Air-conditioning',
        description: 'Refrigeration cycles, psychrometry, and air conditioning',
        estimatedHours: 25,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'vapor-refrigeration',
            name: 'Vapour Refrigeration Cycles',
            description: 'Vapor compression refrigeration cycle',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'gas-refrigeration',
            name: 'Gas Refrigeration and Heat Pump Cycles',
            description: 'Air refrigeration and heat pump cycles',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'moist-air',
            name: 'Properties of Moist Air',
            description: 'Humidity, dew point, and wet bulb temperature',
            estimatedHours: 5,
            difficulty: 'Easy',
            completed: false
          },
          {
            id: 'psychrometric-chart',
            name: 'Psychrometric Chart',
            description: 'Psychrometric chart and its applications',
            estimatedHours: 3,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'psychrometric-processes',
            name: 'Basic Psychrometric Processes',
            description: 'Heating, cooling, humidification, and dehumidification',
            estimatedHours: 3,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'turbomachinery',
        name: 'Turbomachinery',
        description: 'Impulse and reaction principles, turbines',
        estimatedHours: 25,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'impulse-reaction',
            name: 'Impulse and Reaction Principles',
            description: 'Basic principles of turbomachinery',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'velocity-diagrams',
            name: 'Velocity Diagrams',
            description: 'Velocity triangles and energy transfer',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'hydraulic-turbines',
            name: 'Pelton-wheel, Francis and Kaplan Turbines',
            description: 'Hydraulic turbines and their characteristics',
            estimatedHours: 8,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'thermal-turbines',
            name: 'Steam and Gas Turbines',
            description: 'Steam and gas turbine principles',
            estimatedHours: 7,
            difficulty: 'Hard',
            completed: false
          }
        ]
      }
    ]
  },

  // Section 4: Materials, Manufacturing and Industrial Engineering
  {
    id: 'engineering-materials',
    name: 'Engineering Materials',
    section: 'Section 4: Materials, Manufacturing and Industrial Engineering',
    description: 'Structure and properties of materials, phase diagrams, heat treatment, and stress-strain diagrams',
    color: '#0d9488',
    bgColor: '#f0fdfa',
    estimatedHours: 60,
    difficulty: 'Medium',
    completed: 12,
    total: 18,
    topics: [
      {
        id: 'material-structure',
        name: 'Structure and Properties of Engineering Materials',
        description: 'Crystal structure, defects, and material properties',
        estimatedHours: 20,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'crystal-structure',
            name: 'Crystal Structure',
            description: 'Atomic arrangements in crystalline materials',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'material-properties',
            name: 'Material Properties',
            description: 'Mechanical, thermal, and electrical properties',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'defects',
            name: 'Crystal Defects',
            description: 'Point, line, and surface defects',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'phase-diagrams',
        name: 'Phase Diagrams',
        description: 'Equilibrium phase relationships and transformations',
        estimatedHours: 20,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'binary-systems',
            name: 'Binary Phase Diagrams',
            description: 'Two-component phase diagrams and phase rules',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'iron-carbon',
            name: 'Iron-Carbon System',
            description: 'Fe-C phase diagram and steel microstructures',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'heat-treatment',
        name: 'Heat Treatment',
        description: 'Thermal processing of materials for property modification',
        estimatedHours: 15,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'annealing-normalizing',
            name: 'Annealing and Normalizing',
            description: 'Softening heat treatment processes',
            estimatedHours: 5,
            difficulty: 'Easy',
            completed: false
          },
          {
            id: 'hardening-tempering',
            name: 'Hardening and Tempering',
            description: 'Strengthening heat treatment processes',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'surface-treatment',
            name: 'Surface Heat Treatment',
            description: 'Case hardening and surface modification',
            estimatedHours: 2,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'stress-strain',
        name: 'Stress-strain Diagrams for Engineering Materials',
        description: 'Mechanical behavior and testing of materials',
        estimatedHours: 5,
        difficulty: 'Easy',
        completed: true,
        subtopics: [
          {
            id: 'stress-strain-curves',
            name: 'Stress-strain Diagrams',
            description: 'Tensile testing and material behavior',
            estimatedHours: 5,
            difficulty: 'Easy',
            completed: true
          }
        ]
      }
    ]
  },

  {
    id: 'manufacturing-processes',
    name: 'Casting, Forming and Joining Processes',
    section: 'Section 4: Materials, Manufacturing and Industrial Engineering',
    description: 'Casting, plastic deformation, powder metallurgy, and joining processes',
    color: '#ea580c',
    bgColor: '#fff7ed',
    estimatedHours: 90,
    difficulty: 'Medium',
    completed: 15,
    total: 32,
    topics: [
      {
        id: 'casting',
        name: 'Casting Processes',
        description: 'Different types of castings, patterns, molds, and solidification',
        estimatedHours: 25,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'casting-types',
            name: 'Different Types of Castings',
            description: 'Sand casting, investment casting, die casting',
            estimatedHours: 8,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'patterns-molds',
            name: 'Design of Patterns, Moulds and Cores',
            description: 'Pattern design, molding, and core making',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: true
          },
          {
            id: 'solidification',
            name: 'Solidification and Cooling',
            description: 'Solidification process and cooling curves',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'gating-risering',
            name: 'Riser and Gating Design',
            description: 'Design of gating systems and risers',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'forming',
        name: 'Metal Forming',
        description: 'Plastic deformation, yield criteria, hot and cold working',
        estimatedHours: 30,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'plastic-deformation',
            name: 'Plastic Deformation and Yield Criteria',
            description: 'Yield criteria and plastic flow',
            estimatedHours: 6,
            difficulty: 'Hard',
            completed: false
          },
          {
            id: 'hot-cold-working',
            name: 'Fundamentals of Hot and Cold Working',
            description: 'Temperature effects in metal forming',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'bulk-forming',
            name: 'Bulk Forming Processes',
            description: 'Forging, rolling, extrusion, drawing',
            estimatedHours: 12,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'sheet-forming',
            name: 'Sheet Metal Forming',
            description: 'Shearing, deep drawing, bending',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'load-estimation',
        name: 'Load Estimation',
        description: 'Force calculations for forming processes',
        estimatedHours: 10,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'forming-loads',
            name: 'Load Estimation for Metal Forming',
            description: 'Force calculations for bulk and sheet forming',
            estimatedHours: 10,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'powder-metallurgy',
        name: 'Powder Metallurgy',
        description: 'Principles of powder metallurgy',
        estimatedHours: 8,
        difficulty: 'Easy',
        completed: false,
        subtopics: [
          {
            id: 'pm-principles',
            name: 'Principles of Powder Metallurgy',
            description: 'Powder production, compaction, and sintering',
            estimatedHours: 8,
            difficulty: 'Easy',
            completed: false
          }
        ]
      },
      {
        id: 'joining',
        name: 'Joining Processes',
        description: 'Welding, brazing, soldering, and adhesive bonding',
        estimatedHours: 17,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'welding',
            name: 'Principles of Welding',
            description: 'Arc welding, gas welding, and resistance welding',
            estimatedHours: 10,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'brazing-soldering',
            name: 'Brazing and Soldering',
            description: 'Brazing and soldering processes',
            estimatedHours: 4,
            difficulty: 'Easy',
            completed: false
          },
          {
            id: 'adhesive-bonding',
            name: 'Adhesive Bonding',
            description: 'Adhesive joining principles',
            estimatedHours: 3,
            difficulty: 'Easy',
            completed: false
          }
        ]
      }
    ]
  },

  {
    id: 'machining',
    name: 'Machining and Machine Tool Operations',
    section: 'Section 4: Materials, Manufacturing and Industrial Engineering',
    description: 'Machining mechanics, cutting tools, non-traditional machining, and CNC',
    color: '#7c3aed',
    bgColor: '#faf5ff',
    estimatedHours: 70,
    difficulty: 'Hard',
    completed: 8,
    total: 25,
    topics: [
      {
        id: 'machining-mechanics',
        name: 'Mechanics of Machining',
        description: 'Cutting forces, chip formation, and machining parameters',
        estimatedHours: 15,
        difficulty: 'Hard',
        completed: false,
        subtopics: [
          {
            id: 'cutting-mechanics',
            name: 'Mechanics of Machining',
            description: 'Cutting forces, chip formation, and shear angle',
            estimatedHours: 15,
            difficulty: 'Hard',
            completed: false
          }
        ]
      },
      {
        id: 'machine-tools',
        name: 'Basic Machine Tools',
        description: 'Lathe, milling, drilling, and other machine tools',
        estimatedHours: 12,
        difficulty: 'Medium',
        completed: true,
        subtopics: [
          {
            id: 'basic-machines',
            name: 'Basic Machine Tools',
            description: 'Lathe, milling machine, drilling machine operations',
            estimatedHours: 12,
            difficulty: 'Medium',
            completed: true
          }
        ]
      },
      {
        id: 'cutting-tools',
        name: 'Cutting Tools',
        description: 'Single and multi-point tools, geometry, materials, and wear',
        estimatedHours: 18,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'tool-types',
            name: 'Single and Multi-point Cutting Tools',
            description: 'Tool classification and applications',
            estimatedHours: 6,
            difficulty: 'Easy',
            completed: true
          },
          {
            id: 'tool-geometry',
            name: 'Tool Geometry and Materials',
            description: 'Cutting tool angles and tool materials',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          },
          {
            id: 'tool-life',
            name: 'Tool Life and Wear',
            description: 'Tool wear mechanisms and tool life equations',
            estimatedHours: 4,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'machining-economics',
        name: 'Economics of Machining',
        description: 'Cost analysis and optimization in machining',
        estimatedHours: 5,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'cost-analysis',
            name: 'Economics of Machining',
            description: 'Machining cost analysis and optimization',
            estimatedHours: 5,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'non-traditional',
        name: 'Non-traditional Machining',
        description: 'Principles of non-traditional machining processes',
        estimatedHours: 8,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'ntm-principles',
            name: 'Principles of Non-traditional Machining',
            description: 'EDM, ECM, laser machining, and other processes',
            estimatedHours: 8,
            difficulty: 'Medium',
            completed: false
          }
        ]
      },
      {
        id: 'work-holding',
        name: 'Work Holding and Fixtures',
        description: 'Principles of work holding, jigs, and fixtures',
        estimatedHours: 6,
        difficulty: 'Easy',
        completed: false,
        subtopics: [
          {
            id: 'jigs-fixtures',
            name: 'Principles of Work Holding, Jigs and Fixtures',
            description: 'Design and application of work holding devices',
            estimatedHours: 6,
            difficulty: 'Easy',
            completed: false
          }
        ]
      },
      {
        id: 'abrasive-machining',
        name: 'Abrasive Machining',
        description: 'Grinding and other abrasive processes',
        estimatedHours: 6,
        difficulty: 'Medium',
        completed: false,
        subtopics: [
          {
            id: 'grinding',
            name: 'Abrasive Machining Processes',
            description: 'Grinding, honing, lapping, and polishing',
            estimatedHours: 6,
            difficulty: 'Medium',
            completed: false
          }
        ]
      }
    ]
  }
];

export const getSyllabusStats = () => {
  const totalSubjects = gateSubjects.length;
  const totalTopics = gateSubjects.reduce((sum, subject) => sum + subject.topics.length, 0);
  const completedTopics = gateSubjects.reduce((sum, subject) => 
    sum + subject.topics.filter(topic => topic.completed).length, 0
  );
  
  const totalSubtopics = gateSubjects.reduce((sum, subject) => 
    sum + subject.topics.reduce((topicSum, topic) => topicSum + topic.subtopics.length, 0), 0
  );
  
  const completedSubtopics = gateSubjects.reduce((sum, subject) => 
    sum + subject.topics.reduce((topicSum, topic) => 
      topicSum + topic.subtopics.filter(subtopic => subtopic.completed).length, 0
    ), 0
  );

  return {
    totalSubjects,
    totalTopics,
    completedTopics,
    totalSubtopics,
    completedSubtopics,
    overallProgress: Math.round((completedSubtopics / totalSubtopics) * 100)
  };
};

export const getSubjectById = (id: string) => {
  return gateSubjects.find(subject => subject.id === id);
};

export const getTopicById = (subjectId: string, topicId: string) => {
  const subject = getSubjectById(subjectId);
  return subject?.topics.find(topic => topic.id === topicId);
};