import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Clock, FileText, Award, Users, Play, Calendar, Target, Zap, Trophy, CircleCheck as CheckCircle2, Star, TrendingUp } from 'lucide-react-native';

export default function TestsScreen() {
  const [activeTab, setActiveTab] = useState('mock');

  const tabs = [
    { id: 'mock', label: 'Mock Tests', icon: FileText },
    { id: 'topic', label: 'Topic Tests', icon: Target },
    { id: 'challenge', label: 'Daily Challenge', icon: Zap },
  ];

  const mockTests = [
    {
      id: 'gate-2025-mock-1',
      title: 'GATE 2025 Full Mock Test #1',
      description: 'Complete GATE Mechanical Engineering mock test covering all subjects',
      type: 'mock',
      duration: 180,
      totalMarks: 100,
      difficulty: 'Hard',
      subjects: ['Engineering Mathematics', 'Applied Mechanics', 'Thermal Sciences', 'Manufacturing'],
      isAttempted: false,
      questions: 65
    },
    {
      id: 'gate-2024-paper',
      title: 'GATE 2024 Previous Year Paper',
      description: 'Official GATE 2024 Mechanical Engineering question paper',
      type: 'previous-year',
      duration: 180,
      totalMarks: 100,
      difficulty: 'Hard',
      subjects: ['Engineering Mathematics', 'Applied Mechanics', 'Thermal Sciences', 'Manufacturing'],
      isAttempted: true,
      score: 78,
      timeSpent: 165,
      questions: 65
    },
    {
      id: 'thermodynamics-test',
      title: 'Thermodynamics Subject Test',
      description: 'Comprehensive test covering all thermodynamics topics',
      type: 'subject',
      duration: 90,
      totalMarks: 50,
      difficulty: 'Medium',
      subjects: ['Thermodynamics'],
      isAttempted: true,
      score: 42,
      timeSpent: 85,
      questions: 25
    }
  ];

  const topicTests = [
    {
      id: 'linear-algebra-test',
      title: 'Linear Algebra Practice Test',
      description: 'Test covering matrix operations, eigenvalues, and systems of equations',
      type: 'topic',
      duration: 45,
      totalMarks: 25,
      difficulty: 'Medium',
      subjects: ['Engineering Mathematics'],
      isAttempted: false,
      questions: 15
    },
    {
      id: 'heat-transfer-test',
      title: 'Heat Transfer Fundamentals',
      description: 'Conduction, convection, and radiation problems',
      type: 'topic',
      duration: 60,
      totalMarks: 30,
      difficulty: 'Hard',
      subjects: ['Heat Transfer'],
      isAttempted: true,
      score: 26,
      timeSpent: 55,
      questions: 20
    }
  ];

  const dailyChallenge = {
    title: "Today's Challenge",
    description: "Mixed topics from all subjects",
    questions: 10,
    timeLimit: 15,
    xpReward: 100,
    streak: 5,
    completed: false
  };

  const leaderboard = [
    { rank: 1, name: 'Rahul Sharma', score: 95, avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' },
    { rank: 2, name: 'Priya Singh', score: 92, avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' },
    { rank: 3, name: 'Amit Kumar', score: 89, avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' },
    { rank: 4, name: 'You', score: 78, avatar: null, isCurrentUser: true }
  ];

  const testStats = {
    totalTests: mockTests.length + topicTests.length,
    attemptedTests: [...mockTests, ...topicTests].filter(test => test.isAttempted).length,
    averageScore: 78,
    mockTestsAttempted: mockTests.filter(test => test.isAttempted).length,
    topicTestsAttempted: topicTests.filter(test => test.isAttempted).length
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return '#059669';
      case 'Medium': return '#ea580c';
      case 'Hard': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'mock': return '#1e40af';
      case 'subject': return '#7c3aed';
      case 'topic': return '#059669';
      case 'previous-year': return '#dc2626';
      default: return '#6b7280';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Tests & Practice</Text>
        <Text style={styles.subtitle}>Evaluate your GATE preparation</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[styles.tab, activeTab === tab.id && styles.activeTab]}
            onPress={() => setActiveTab(tab.id)}
          >
            <tab.icon 
              size={18} 
              color={activeTab === tab.id ? '#1e40af' : '#6b7280'} 
            />
            <Text 
              style={[
                styles.tabText, 
                activeTab === tab.id && styles.activeTabText
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {activeTab === 'mock' && (
          <>
            {/* Stats Overview */}
            <View style={styles.statsContainer}>
              <View style={styles.statCard}>
                <FileText size={20} color="#1e40af" />
                <Text style={styles.statNumber}>{testStats.attemptedTests}</Text>
                <Text style={styles.statLabel}>Tests Taken</Text>
              </View>
              <View style={styles.statCard}>
                <Award size={20} color="#059669" />
                <Text style={styles.statNumber}>{testStats.averageScore}%</Text>
                <Text style={styles.statLabel}>Avg Score</Text>
              </View>
              <View style={styles.statCard}>
                <Trophy size={20} color="#f59e0b" />
                <Text style={styles.statNumber}>652</Text>
                <Text style={styles.statLabel}>All India Rank</Text>
              </View>
            </View>

            {/* Mock Tests List */}
            <View style={styles.testsContainer}>
              <Text style={styles.sectionTitle}>Available Mock Tests</Text>
              {mockTests.map((test) => (
                <View key={test.id} style={styles.testCard}>
                  <View style={styles.testHeader}>
                    <View style={styles.testInfo}>
                      <Text style={styles.testTitle}>{test.title}</Text>
                      <Text style={styles.testDescription}>{test.description}</Text>
                      <View style={styles.testMeta}>
                        <View style={styles.metaItem}>
                          <FileText size={14} color="#6b7280" />
                          <Text style={styles.metaText}>{test.questions} questions</Text>
                        </View>
                        <View style={styles.metaItem}>
                          <Clock size={14} color="#6b7280" />
                          <Text style={styles.metaText}>{test.duration} min</Text>
                        </View>
                        <View style={styles.metaItem}>
                          <Users size={14} color="#6b7280" />
                          <Text style={styles.metaText}>1.2K taken</Text>
                        </View>
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.testBadges}>
                    <View style={[styles.badge, { backgroundColor: getTypeColor(test.type) + '15' }]}>
                      <Text style={[styles.badgeText, { color: getTypeColor(test.type) }]}>
                        {test.type === 'mock' ? 'Full Length' : test.type}
                      </Text>
                    </View>
                    <View style={[styles.badge, { backgroundColor: getDifficultyColor(test.difficulty) + '15' }]}>
                      <Text style={[styles.badgeText, { color: getDifficultyColor(test.difficulty) }]}>
                        {test.difficulty}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.testFooter}>
                    <View style={styles.testStats}>
                      {test.isAttempted ? (
                        <View style={styles.scoreContainer}>
                          <Award size={16} color="#059669" />
                          <Text style={styles.scoreText}>Your Score: {test.score}%</Text>
                        </View>
                      ) : (
                        <View style={styles.statusContainer}>
                          <Text style={styles.notAttemptedText}>Not attempted</Text>
                        </View>
                      )}
                      <Text style={styles.avgScoreText}>Avg: 68%</Text>
                    </View>
                    <TouchableOpacity style={styles.startButton}>
                      <Play size={16} color="#ffffff" />
                      <Text style={styles.startButtonText}>
                        {test.isAttempted ? 'Retake' : 'Start'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>

            {/* Leaderboard */}
            <View style={styles.leaderboardContainer}>
              <Text style={styles.sectionTitle}>Leaderboard</Text>
              <View style={styles.leaderboardCard}>
                {leaderboard.map((user) => (
                  <View key={user.rank} style={[
                    styles.leaderboardItem,
                    user.isCurrentUser && styles.currentUserItem
                  ]}>
                    <View style={styles.rankContainer}>
                      <Text style={[
                        styles.rankText,
                        user.rank <= 3 && styles.topRankText
                      ]}>
                        {user.rank}
                      </Text>
                      {user.rank <= 3 && (
                        <Star size={12} color="#f59e0b" fill="#f59e0b" />
                      )}
                    </View>
                    <View style={styles.userInfo}>
                      {user.avatar ? (
                        <Image source={{ uri: user.avatar }} style={styles.userAvatar} />
                      ) : (
                        <View style={styles.userAvatarPlaceholder}>
                          <Text style={styles.userAvatarText}>Y</Text>
                        </View>
                      )}
                      <Text style={[
                        styles.userName,
                        user.isCurrentUser && styles.currentUserName
                      ]}>
                        {user.name}
                      </Text>
                    </View>
                    <Text style={styles.userScore}>{user.score}%</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        )}

        {activeTab === 'topic' && (
          <>
            {/* Topic Test Stats */}
            <View style={styles.topicStatsContainer}>
              <View style={styles.topicStatCard}>
                <Target size={20} color="#7c3aed" />
                <Text style={styles.topicStatNumber}>45</Text>
                <Text style={styles.topicStatLabel}>Topics Tested</Text>
              </View>
              <View style={styles.topicStatCard}>
                <Zap size={20} color="#f59e0b" />
                <Text style={styles.topicStatNumber}>1,850</Text>
                <Text style={styles.topicStatLabel}>XP Earned</Text>
              </View>
            </View>

            {/* Topic Tests */}
            <View style={styles.topicTestsContainer}>
              <Text style={styles.sectionTitle}>Topic-wise Tests</Text>
              {topicTests.map((test) => (
                <TouchableOpacity key={test.id} style={styles.topicTestCard}>
                  <View style={styles.topicTestHeader}>
                    <View style={styles.topicTestInfo}>
                      <Text style={styles.topicTestTitle}>{test.title}</Text>
                      <Text style={styles.topicTestSubject}>{test.subjects[0]}</Text>
                      <View style={styles.topicTestMeta}>
                        <Text style={styles.topicTestQuestions}>{test.questions} questions</Text>
                        <Text style={styles.topicTestDuration}>{test.duration} min</Text>
                        <View style={[styles.topicDifficultyBadge, { backgroundColor: getDifficultyColor(test.difficulty) + '15' }]}>
                          <Text style={[styles.topicDifficultyText, { color: getDifficultyColor(test.difficulty) }]}>
                            {test.difficulty}
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View style={styles.topicTestActions}>
                      <View style={styles.xpBadge}>
                        <Zap size={12} color="#f59e0b" />
                        <Text style={styles.xpText}>150 XP</Text>
                      </View>
                      {test.isAttempted ? (
                        <View style={styles.completedContainer}>
                          <CheckCircle2 size={16} color="#059669" />
                          <Text style={styles.completedScore}>{test.score}%</Text>
                        </View>
                      ) : (
                        <TouchableOpacity style={styles.topicStartButton}>
                          <Play size={16} color="#ffffff" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </>
        )}

        {activeTab === 'challenge' && (
          <>
            {/* Daily Challenge */}
            <View style={styles.challengeContainer}>
              <Text style={styles.sectionTitle}>Daily Challenge</Text>
              <View style={styles.challengeCard}>
                <View style={styles.challengeHeader}>
                  <View style={styles.challengeIcon}>
                    <Zap size={24} color="#f59e0b" />
                  </View>
                  <View style={styles.challengeInfo}>
                    <Text style={styles.challengeTitle}>{dailyChallenge.title}</Text>
                    <Text style={styles.challengeDescription}>{dailyChallenge.description}</Text>
                  </View>
                  <View style={styles.streakBadge}>
                    <Text style={styles.streakNumber}>{dailyChallenge.streak}</Text>
                    <Text style={styles.streakLabel}>day streak</Text>
                  </View>
                </View>
                
                <View style={styles.challengeDetails}>
                  <View style={styles.challengeDetail}>
                    <FileText size={16} color="#6b7280" />
                    <Text style={styles.challengeDetailText}>{dailyChallenge.questions} questions</Text>
                  </View>
                  <View style={styles.challengeDetail}>
                    <Clock size={16} color="#6b7280" />
                    <Text style={styles.challengeDetailText}>{dailyChallenge.timeLimit} minutes</Text>
                  </View>
                  <View style={styles.challengeDetail}>
                    <Zap size={16} color="#f59e0b" />
                    <Text style={styles.challengeDetailText}>{dailyChallenge.xpReward} XP reward</Text>
                  </View>
                </View>

                <TouchableOpacity style={styles.challengeButton}>
                  <Play size={18} color="#ffffff" />
                  <Text style={styles.challengeButtonText}>Start Challenge</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Challenge History */}
            <View style={styles.challengeHistoryContainer}>
              <Text style={styles.sectionTitle}>Challenge History</Text>
              <View style={styles.historyCard}>
                <View style={styles.historyItem}>
                  <View style={styles.historyDate}>
                    <Text style={styles.historyDay}>Yesterday</Text>
                  </View>
                  <View style={styles.historyDetails}>
                    <Text style={styles.historyScore}>8/10 correct</Text>
                    <Text style={styles.historyXP}>+80 XP</Text>
                  </View>
                  <CheckCircle2 size={20} color="#059669" />
                </View>
                
                <View style={styles.historyItem}>
                  <View style={styles.historyDate}>
                    <Text style={styles.historyDay}>2 days ago</Text>
                  </View>
                  <View style={styles.historyDetails}>
                    <Text style={styles.historyScore}>9/10 correct</Text>
                    <Text style={styles.historyXP}>+90 XP</Text>
                  </View>
                  <CheckCircle2 size={20} color="#059669" />
                </View>
                
                <View style={styles.historyItem}>
                  <View style={styles.historyDate}>
                    <Text style={styles.historyDay}>3 days ago</Text>
                  </View>
                  <View style={styles.historyDetails}>
                    <Text style={styles.historyScore}>7/10 correct</Text>
                    <Text style={styles.historyXP}>+70 XP</Text>
                  </View>
                  <CheckCircle2 size={20} color="#059669" />
                </View>
              </View>
            </View>

            {/* Weekly Progress */}
            <View style={styles.weeklyProgressContainer}>
              <Text style={styles.sectionTitle}>Weekly Progress</Text>
              <View style={styles.weeklyProgressCard}>
                <View style={styles.progressStat}>
                  <TrendingUp size={20} color="#1e40af" />
                  <Text style={styles.progressStatNumber}>85%</Text>
                  <Text style={styles.progressStatLabel}>Completion Rate</Text>
                </View>
                <View style={styles.progressStat}>
                  <Zap size={20} color="#f59e0b" />
                  <Text style={styles.progressStatNumber}>560</Text>
                  <Text style={styles.progressStatLabel}>XP This Week</Text>
                </View>
              </View>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    gap: 6,
  },
  activeTab: {
    backgroundColor: '#eff6ff',
    borderWidth: 1,
    borderColor: '#dbeafe',
  },
  tabText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activeTabText: {
    color: '#1e40af',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
  testsContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  testCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  testHeader: {
    marginBottom: 12,
  },
  testInfo: {
    marginBottom: 8,
  },
  testTitle: {
    fontSize: 16,
    fontFamily: 'RobotoSlab-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  testDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  testMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  testBadges: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  badge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  badgeText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
  },
  testFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  testStats: {
    flex: 1,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  scoreText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
  statusContainer: {
    marginBottom: 4,
  },
  notAttemptedText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  avgScoreText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1e40af',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
    gap: 8,
  },
  startButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  leaderboardContainer: {
    marginBottom: 24,
  },
  leaderboardCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  currentUserItem: {
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    paddingHorizontal: 8,
    marginHorizontal: -8,
  },
  rankContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 40,
    gap: 4,
  },
  rankText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#6b7280',
  },
  topRankText: {
    color: '#f59e0b',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 12,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  userAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#1e40af',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userAvatarText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  userName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  currentUserName: {
    color: '#1e40af',
  },
  userScore: {
    fontSize: 14,
    fontFamily: 'RobotoSlab-Bold',
    color: '#059669',
  },
  topicStatsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  topicStatCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  topicStatNumber: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  topicStatLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
  topicTestsContainer: {
    marginBottom: 24,
  },
  topicTestCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  topicTestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  topicTestInfo: {
    flex: 1,
  },
  topicTestTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  topicTestSubject: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  topicTestMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  topicTestQuestions: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  topicTestDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  topicDifficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  topicDifficultyText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
  },
  topicTestActions: {
    alignItems: 'center',
    gap: 8,
  },
  xpBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  xpText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#92400e',
  },
  completedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  completedScore: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
  topicStartButton: {
    width: 36,
    height: 36,
    backgroundColor: '#1e40af',
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  challengeContainer: {
    marginBottom: 24,
  },
  challengeCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  challengeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  challengeIcon: {
    width: 48,
    height: 48,
    backgroundColor: '#fef3c7',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  challengeInfo: {
    flex: 1,
  },
  challengeTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  challengeDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  streakBadge: {
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
  },
  streakNumber: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#92400e',
  },
  streakLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#92400e',
  },
  challengeDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  challengeDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  challengeDetailText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  challengeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#1e40af',
    paddingVertical: 14,
    borderRadius: 12,
    gap: 8,
  },
  challengeButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  challengeHistoryContainer: {
    marginBottom: 24,
  },
  historyCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  historyDate: {
    width: 80,
  },
  historyDay: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  historyDetails: {
    flex: 1,
    marginLeft: 12,
  },
  historyScore: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  historyXP: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#f59e0b',
  },
  weeklyProgressContainer: {
    marginBottom: 24,
  },
  weeklyProgressCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressStat: {
    alignItems: 'center',
  },
  progressStatNumber: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  progressStatLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
});