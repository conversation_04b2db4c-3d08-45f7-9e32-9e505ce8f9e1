/*
  # Notifications and Communication Schema
  
  1. Tables Created
    - notification_types: Different types of notifications
    - notifications: User notifications and alerts
    - notification_preferences: User notification settings
    - announcements: System-wide announcements
    - user_communications: Direct messages and communications
    - push_tokens: Device tokens for push notifications
    - email_queue: Queue for email notifications
    - notification_analytics: Analytics on notification effectiveness
    
  2. Features
    - Comprehensive notification system
    - User preference management
    - System announcements
    - Direct messaging capabilities
    - Push notification token management
    - Email queue system
    - Notification analytics and tracking
*/

-- Notification types and categories
CREATE TABLE IF NOT EXISTS notification_types (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  category text NOT NULL CHECK (category IN ('system', 'study', 'test', 'achievement', 'social', 'reminder', 'marketing')),
  icon text DEFAULT 'bell',
  color text DEFAULT '#3b82f6',
  default_enabled boolean DEFAULT true,
  can_disable boolean DEFAULT true,
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- User notifications
CREATE TABLE IF NOT EXISTS notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  type_id uuid REFERENCES notification_types(id),
  title text NOT NULL,
  message text NOT NULL,
  action_url text,
  action_data jsonb DEFAULT '{}',
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  is_read boolean DEFAULT false,
  is_clicked boolean DEFAULT false,
  read_at timestamptz,
  clicked_at timestamptz,
  expires_at timestamptz,
  delivery_channels text[] DEFAULT '{"in-app"}', -- in-app, push, email, sms
  delivery_status jsonb DEFAULT '{}', -- Status for each channel
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- User notification preferences
CREATE TABLE IF NOT EXISTS notification_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  type_id uuid REFERENCES notification_types(id),
  in_app_enabled boolean DEFAULT true,
  push_enabled boolean DEFAULT true,
  email_enabled boolean DEFAULT true,
  sms_enabled boolean DEFAULT false,
  quiet_hours_start time DEFAULT '22:00:00',
  quiet_hours_end time DEFAULT '08:00:00',
  frequency text DEFAULT 'immediate' CHECK (frequency IN ('immediate', 'hourly', 'daily', 'weekly', 'never')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, type_id)
);

-- System-wide announcements
CREATE TABLE IF NOT EXISTS announcements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  announcement_type text DEFAULT 'general' CHECK (announcement_type IN ('general', 'maintenance', 'feature', 'exam-update', 'emergency')),
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  target_audience text DEFAULT 'all' CHECK (target_audience IN ('all', 'students', 'premium', 'specific')),
  target_criteria jsonb DEFAULT '{}', -- Specific targeting criteria
  is_published boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  show_popup boolean DEFAULT false,
  start_date timestamptz DEFAULT now(),
  end_date timestamptz,
  image_url text,
  action_button_text text,
  action_button_url text,
  view_count integer DEFAULT 0,
  click_count integer DEFAULT 0,
  created_by uuid REFERENCES users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User communications and messages
CREATE TABLE IF NOT EXISTS user_communications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id uuid REFERENCES users(id),
  recipient_id uuid REFERENCES users(id) ON DELETE CASCADE,
  subject text,
  message text NOT NULL,
  message_type text DEFAULT 'direct' CHECK (message_type IN ('direct', 'support', 'feedback', 'report')),
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  is_read boolean DEFAULT false,
  is_replied boolean DEFAULT false,
  parent_message_id uuid REFERENCES user_communications(id),
  attachments jsonb DEFAULT '[]',
  read_at timestamptz,
  replied_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Push notification tokens for devices
CREATE TABLE IF NOT EXISTS push_tokens (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  token text NOT NULL,
  platform text NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
  device_id text,
  device_name text,
  app_version text,
  os_version text,
  is_active boolean DEFAULT true,
  last_used_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, token)
);

-- Email notification queue
CREATE TABLE IF NOT EXISTS email_queue (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  to_email text NOT NULL,
  subject text NOT NULL,
  html_content text NOT NULL,
  text_content text,
  email_type text NOT NULL,
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'sent', 'failed', 'cancelled')),
  scheduled_at timestamptz DEFAULT now(),
  sent_at timestamptz,
  failed_at timestamptz,
  error_message text,
  retry_count integer DEFAULT 0,
  max_retries integer DEFAULT 3,
  template_data jsonb DEFAULT '{}',
  tracking_data jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Notification analytics
CREATE TABLE IF NOT EXISTS notification_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_type text NOT NULL,
  delivery_channel text NOT NULL,
  metric_name text NOT NULL,
  metric_value integer NOT NULL DEFAULT 0,
  time_period date NOT NULL,
  additional_data jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  UNIQUE(notification_type, delivery_channel, metric_name, time_period)
);

-- Enable Row Level Security
ALTER TABLE notification_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notification_types (public read)
CREATE POLICY "Anyone can read notification types"
  ON notification_types
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for notifications
CREATE POLICY "Users can read own notifications"
  ON notifications
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications"
  ON notifications
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for notification_preferences
CREATE POLICY "Users can manage own notification preferences"
  ON notification_preferences
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for announcements (public read for published)
CREATE POLICY "Anyone can read published announcements"
  ON announcements
  FOR SELECT
  TO authenticated
  USING (is_published = true AND (end_date IS NULL OR end_date > now()));

-- RLS Policies for user_communications
CREATE POLICY "Users can read own communications"
  ON user_communications
  FOR SELECT
  TO authenticated
  USING (auth.uid() = recipient_id OR auth.uid() = sender_id);

CREATE POLICY "Users can send communications"
  ON user_communications
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update received communications"
  ON user_communications
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = recipient_id);

-- RLS Policies for push_tokens
CREATE POLICY "Users can manage own push tokens"
  ON push_tokens
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for email_queue
CREATE POLICY "Users can read own email queue"
  ON email_queue
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for notification_analytics (admin only)
CREATE POLICY "No access to notification analytics"
  ON notification_analytics
  FOR ALL
  TO authenticated
  USING (false);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_types_category ON notification_types(category);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type_id ON notifications(type_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_announcements_published ON announcements(is_published);
CREATE INDEX IF NOT EXISTS idx_announcements_dates ON announcements(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_user_communications_recipient ON user_communications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_user_communications_sender ON user_communications(sender_id);
CREATE INDEX IF NOT EXISTS idx_user_communications_read ON user_communications(is_read);
CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_active ON push_tokens(is_active);
CREATE INDEX IF NOT EXISTS idx_email_queue_status ON email_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_queue_scheduled ON email_queue(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_period ON notification_analytics(time_period);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_notification_preferences_updated_at
  BEFORE UPDATE ON notification_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_announcements_updated_at
  BEFORE UPDATE ON announcements
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();