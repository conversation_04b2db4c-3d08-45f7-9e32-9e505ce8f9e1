/*
  # Achievements and Gamification Schema
  
  1. Tables Created
    - achievement_types: Different categories of achievements
    - achievements: Available achievements with criteria
    - user_achievements: User's earned achievements
    - xp_transactions: XP earning and spending history
    - leaderboards: Various leaderboard rankings
    - badges: Special badges and their criteria
    - user_badges: User's earned badges
    - challenges: Special challenges and competitions
    
  2. Features
    - Comprehensive achievement system
    - XP (Experience Points) tracking and transactions
    - Multiple leaderboard categories
    - Badge system for special accomplishments
    - Challenge system for engagement
    - Detailed progress tracking and rewards
*/

-- Achievement types/categories
CREATE TABLE IF NOT EXISTS achievement_types (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  icon text DEFAULT 'trophy',
  color text DEFAULT '#f59e0b',
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Available achievements
CREATE TABLE IF NOT EXISTS achievements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  type_id uuid REFERENCES achievement_types(id),
  name text NOT NULL,
  description text NOT NULL,
  icon text DEFAULT 'award',
  criteria jsonb NOT NULL, -- Conditions to earn this achievement
  xp_reward integer DEFAULT 100,
  badge_reward uuid, -- Reference to badges table
  rarity text DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
  is_hidden boolean DEFAULT false, -- Hidden until earned
  is_repeatable boolean DEFAULT false,
  max_repetitions integer DEFAULT 1,
  prerequisite_achievements uuid[] DEFAULT '{}',
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User earned achievements
CREATE TABLE IF NOT EXISTS user_achievements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  achievement_id uuid REFERENCES achievements(id) ON DELETE CASCADE,
  earned_at timestamptz DEFAULT now(),
  progress_data jsonb DEFAULT '{}', -- Track progress towards achievement
  repetition_count integer DEFAULT 1,
  xp_earned integer DEFAULT 0,
  is_featured boolean DEFAULT false, -- Featured on profile
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, achievement_id, repetition_count)
);

-- XP (Experience Points) transactions
CREATE TABLE IF NOT EXISTS xp_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  transaction_type text NOT NULL CHECK (transaction_type IN ('earned', 'spent', 'bonus', 'penalty')),
  amount integer NOT NULL,
  source_type text NOT NULL CHECK (source_type IN ('study', 'test', 'achievement', 'streak', 'challenge', 'daily-goal', 'purchase')),
  source_id uuid, -- ID of the source (test_id, achievement_id, etc.)
  description text NOT NULL,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Leaderboards for different categories
CREATE TABLE IF NOT EXISTS leaderboards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  leaderboard_type text NOT NULL CHECK (leaderboard_type IN ('overall', 'weekly', 'monthly', 'subject-wise', 'test-score', 'study-hours', 'streak')),
  category text, -- Subject name for subject-wise leaderboards
  score integer NOT NULL DEFAULT 0,
  rank_position integer,
  period_start date,
  period_end date,
  last_updated_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, leaderboard_type, category, period_start)
);

-- Badges system
CREATE TABLE IF NOT EXISTS badges (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  icon text DEFAULT 'shield',
  color text DEFAULT '#3b82f6',
  criteria jsonb NOT NULL,
  rarity text DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
  is_limited_time boolean DEFAULT false,
  available_from timestamptz,
  available_until timestamptz,
  max_recipients integer, -- Limit number of people who can earn this badge
  current_recipients integer DEFAULT 0,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User earned badges
CREATE TABLE IF NOT EXISTS user_badges (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  badge_id uuid REFERENCES badges(id) ON DELETE CASCADE,
  earned_at timestamptz DEFAULT now(),
  is_displayed boolean DEFAULT true, -- Show on profile
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, badge_id)
);

-- Challenges and competitions
CREATE TABLE IF NOT EXISTS challenges (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  challenge_type text DEFAULT 'individual' CHECK (challenge_type IN ('individual', 'group', 'global')),
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  criteria jsonb NOT NULL,
  rewards jsonb DEFAULT '{}', -- XP, badges, achievements
  start_date timestamptz NOT NULL,
  end_date timestamptz NOT NULL,
  max_participants integer,
  current_participants integer DEFAULT 0,
  is_featured boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User challenge participation
CREATE TABLE IF NOT EXISTS user_challenge_participation (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  challenge_id uuid REFERENCES challenges(id) ON DELETE CASCADE,
  joined_at timestamptz DEFAULT now(),
  progress_data jsonb DEFAULT '{}',
  is_completed boolean DEFAULT false,
  completed_at timestamptz,
  rank_achieved integer,
  rewards_claimed boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, challenge_id)
);

-- Enable Row Level Security
ALTER TABLE achievement_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE xp_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_challenge_participation ENABLE ROW LEVEL SECURITY;

-- RLS Policies for achievement_types (public read)
CREATE POLICY "Anyone can read achievement types"
  ON achievement_types
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for achievements (public read for active achievements)
CREATE POLICY "Anyone can read active achievements"
  ON achievements
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- RLS Policies for user_achievements
CREATE POLICY "Users can read own achievements"
  ON user_achievements
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own achievements"
  ON user_achievements
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for xp_transactions
CREATE POLICY "Users can read own XP transactions"
  ON xp_transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own XP transactions"
  ON xp_transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for leaderboards (public read)
CREATE POLICY "Anyone can read leaderboards"
  ON leaderboards
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for badges (public read for active badges)
CREATE POLICY "Anyone can read active badges"
  ON badges
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- RLS Policies for user_badges
CREATE POLICY "Users can read own badges"
  ON user_badges
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own badges"
  ON user_badges
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own badges"
  ON user_badges
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for challenges (public read for active challenges)
CREATE POLICY "Anyone can read active challenges"
  ON challenges
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- RLS Policies for user_challenge_participation
CREATE POLICY "Users can manage own challenge participation"
  ON user_challenge_participation
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_achievements_type ON achievements(type_id);
CREATE INDEX IF NOT EXISTS idx_achievements_active ON achievements(is_active);
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON user_achievements(achievement_id);
CREATE INDEX IF NOT EXISTS idx_xp_transactions_user_id ON xp_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_xp_transactions_type ON xp_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_xp_transactions_source ON xp_transactions(source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_leaderboards_type ON leaderboards(leaderboard_type);
CREATE INDEX IF NOT EXISTS idx_leaderboards_user_id ON leaderboards(user_id);
CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);
CREATE INDEX IF NOT EXISTS idx_user_badges_user_id ON user_badges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_badges_badge_id ON user_badges(badge_id);
CREATE INDEX IF NOT EXISTS idx_challenges_active ON challenges(is_active);
CREATE INDEX IF NOT EXISTS idx_challenges_dates ON challenges(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_user_challenge_participation_user_id ON user_challenge_participation(user_id);
CREATE INDEX IF NOT EXISTS idx_user_challenge_participation_challenge_id ON user_challenge_participation(challenge_id);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_achievements_updated_at
  BEFORE UPDATE ON achievements
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_badges_updated_at
  BEFORE UPDATE ON badges
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_challenges_updated_at
  BEFORE UPDATE ON challenges
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_challenge_participation_updated_at
  BEFORE UPDATE ON user_challenge_participation
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();