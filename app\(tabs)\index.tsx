import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, Image, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Clock, Target, Award, BookOpen, FileText, TrendingUp, Calendar, Play, Users, Zap,
  ChevronRight, Bot, Youtube, CircleCheck as CheckCircle2, TriangleAlert as AlertTriangle,
  Search, Bell, Video, MessageCircle, Trophy, BarChart3, Settings, HelpCircle
} from 'lucide-react-native';
import { router } from 'expo-router';
import { useAuth } from '@/components/AuthContext';
import { getProgressStats } from '@/data/progress';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [stats, setStats] = useState([
    { label: 'Study Streak', value: '0 days', icon: Target, color: '#059669', bgColor: '#ecfdf5' },
    { label: 'Tests Completed', value: '0', icon: FileText, color: '#1e40af', bgColor: '#eff6ff' },
    { label: 'Avg. Score', value: '0%', icon: Award, color: '#ea580c', bgColor: '#fff7ed' },
    { label: 'XP Points', value: '0', icon: Zap, color: '#7c3aed', bgColor: '#faf5ff' },
  ]);

  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (user && isAuthenticated) {
      loadUserStats();
    }
  }, [user, isAuthenticated]);

  const loadUserStats = async () => {
    if (!user) return;

    try {
      const progressStats = await getProgressStats(user.id);

      setStats([
        {
          label: 'Study Streak',
          value: `${progressStats.currentStreak} days`,
          icon: Target,
          color: '#059669',
          bgColor: '#ecfdf5'
        },
        {
          label: 'Tests Completed',
          value: progressStats.totalSessions.toString(),
          icon: FileText,
          color: '#1e40af',
          bgColor: '#eff6ff'
        },
        {
          label: 'Avg. Score',
          value: `${progressStats.averageScore}%`,
          icon: Award,
          color: '#ea580c',
          bgColor: '#fff7ed'
        },
        {
          label: 'XP Points',
          value: progressStats.totalXP.toLocaleString(),
          icon: Zap,
          color: '#7c3aed',
          bgColor: '#faf5ff'
        },
      ]);
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const quickActions = [
    {
      title: 'Continue Learning',
      subtitle: 'Engineering Mathematics - Linear Algebra',
      icon: Play,
      color: '#1e40af',
      bgColor: '#eff6ff',
      route: '/subjects'
    },
    {
      title: 'Daily Quiz',
      subtitle: '10 questions • Earn 100 XP',
      icon: Target,
      color: '#059669',
      bgColor: '#ecfdf5',
      route: '/tests'
    },
    {
      title: 'AI Doubt Solver',
      subtitle: 'Ask questions to AI tutor',
      icon: Bot,
      color: '#7c3aed',
      bgColor: '#faf5ff',
      route: '/ai-tutor'
    },
    {
      title: 'Mock Test',
      subtitle: 'GATE 2025 Full Length Test',
      icon: Calendar,
      color: '#ea580c',
      bgColor: '#fff7ed',
      route: '/tests'
    },
  ];

  const additionalFeatures = [
    {
      title: 'Video Lectures',
      description: 'Expert video content',
      icon: Video,
      color: '#dc2626',
      bgColor: '#fef2f2',
      route: '/videos'
    },
    {
      title: 'Study Schedule',
      description: 'Plan your preparation',
      icon: Calendar,
      color: '#059669',
      bgColor: '#ecfdf5',
      route: '/schedule'
    },
    {
      title: 'Progress Analytics',
      description: 'Track your performance',
      icon: BarChart3,
      color: '#7c3aed',
      bgColor: '#faf5ff',
      route: '/progress'
    },
    {
      title: 'Community',
      description: 'Connect with peers',
      icon: MessageCircle,
      color: '#ea580c',
      bgColor: '#fff7ed',
      route: '/community'
    },
    {
      title: 'Leaderboard',
      description: 'See your ranking',
      icon: Trophy,
      color: '#f59e0b',
      bgColor: '#fefbeb',
      route: '/leaderboard'
    },
    {
      title: 'Settings',
      description: 'App preferences',
      icon: Settings,
      color: '#6b7280',
      bgColor: '#f9fafb',
      route: '/settings'
    }
  ];

  const recentVideos = [
    {
      title: 'Linear Algebra Fundamentals',
      channel: 'GATE Academy',
      duration: '45:30',
      thumbnail: 'https://images.pexels.com/photos/8199562/pexels-photo-8199562.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop',
      progress: 65
    },
    {
      title: 'Thermodynamics First Law',
      channel: 'Engineering Hub',
      duration: '38:15',
      thumbnail: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop',
      progress: 30
    },
    {
      title: 'Strength of Materials Basics',
      channel: 'Mechanical Prep',
      duration: '52:20',
      thumbnail: 'https://images.pexels.com/photos/256541/pexels-photo-256541.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop',
      progress: 0
    }
  ];

  const strengths = [
    { topic: 'Linear Algebra', score: 92, subject: 'Mathematics' },
    { topic: 'IC Engines', score: 88, subject: 'Applications' },
    { topic: 'Casting', score: 85, subject: 'Manufacturing' }
  ];

  const weaknesses = [
    { topic: 'Complex Variables', score: 58, subject: 'Mathematics' },
    { topic: 'Vibrations', score: 62, subject: 'Theory of Machines' },
    { topic: 'Heat Exchangers', score: 65, subject: 'Heat Transfer' }
  ];

  const upcomingDeadlines = [
    { title: 'GATE 2025 Registration', date: 'Aug 24, 2024', type: 'registration', urgent: true },
    { title: 'Mock Test Series', date: 'Sep 15, 2024', type: 'test', urgent: false },
    { title: 'Syllabus Completion Target', date: 'Dec 31, 2024', type: 'milestone', urgent: false }
  ];

  const handleFeaturePress = (route: string) => {
    router.push(route as any);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>
              {new Date().getHours() < 12 ? 'Good Morning!' :
               new Date().getHours() < 18 ? 'Good Afternoon!' : 'Good Evening!'}
            </Text>
            <Text style={styles.userName}>
              {user ? `Welcome back, ${user.fullName.split(' ')[0]}! 🚀` : 'Ready for GATE 2025? 🚀'}
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.notificationButton}>
              <Bell size={20} color="#6b7280" />
              <View style={styles.notificationBadge} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.profileButton}
              onPress={() => router.push('/profile')}
            >
              <Text style={styles.profileInitial}>
                {user ? user.fullName.charAt(0).toUpperCase() : 'U'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBox}>
            <Search size={20} color="#6b7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search topics, videos, tests..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#9ca3af"
            />
          </View>
        </View>

        {/* Progress Card */}
        <LinearGradient
          colors={['#1e40af', '#1e3a8a']}
          style={styles.progressCard}
        >
          <View style={styles.progressHeader}>
            <View>
              <Text style={styles.progressTitle}>GATE 2025 Journey</Text>
              <Text style={styles.progressSubtitle}>Mechanical Engineering</Text>
            </View>
            <View style={styles.daysLeft}>
              <Text style={styles.daysNumber}>156</Text>
              <Text style={styles.daysLabel}>days left</Text>
            </View>
          </View>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '68%' }]} />
          </View>
          <Text style={styles.progressText}>
            68% Syllabus Complete • 142/210 Topics Done
          </Text>
        </LinearGradient>

        {/* Stats Grid */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <TouchableOpacity key={index} style={[styles.statCard, { backgroundColor: stat.bgColor }]}>
                <View style={styles.statIcon}>
                  <stat.icon size={20} color={stat.color} />
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={styles.actionCard}
              onPress={() => handleFeaturePress(action.route)}
            >
              <View style={[styles.actionIcon, { backgroundColor: action.bgColor }]}>
                <action.icon size={20} color={action.color} />
              </View>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>{action.title}</Text>
                <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
              </View>
              <ChevronRight size={20} color="#9ca3af" />
            </TouchableOpacity>
          ))}
        </View>

        {/* Additional Features */}
        <View style={styles.featuresContainer}>
          <Text style={styles.sectionTitle}>More Features</Text>
          <View style={styles.featuresGrid}>
            {additionalFeatures.map((feature, index) => (
              <TouchableOpacity
                key={index}
                style={styles.featureCard}
                onPress={() => handleFeaturePress(feature.route)}
              >
                <View style={[styles.featureIcon, { backgroundColor: feature.bgColor }]}>
                  <feature.icon size={20} color={feature.color} />
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Videos */}
        <View style={styles.videosContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Continue Watching</Text>
            <TouchableOpacity onPress={() => handleFeaturePress('/videos')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.videosScroll}>
            {recentVideos.map((video, index) => (
              <TouchableOpacity key={index} style={styles.videoCard}>
                <View style={styles.videoThumbnail}>
                  <Image source={{ uri: video.thumbnail }} style={styles.thumbnailImage} />
                  <View style={styles.playOverlay}>
                    <Play size={24} color="#ffffff" fill="#ffffff" />
                  </View>
                  <View style={styles.durationBadge}>
                    <Text style={styles.durationText}>{video.duration}</Text>
                  </View>
                  {video.progress > 0 && (
                    <View style={styles.progressOverlay}>
                      <View style={[styles.videoProgress, { width: `${video.progress}%` }]} />
                    </View>
                  )}
                </View>
                <View style={styles.videoInfo}>
                  <Text style={styles.videoTitle} numberOfLines={2}>{video.title}</Text>
                  <View style={styles.videoMeta}>
                    <Youtube size={12} color="#ff0000" />
                    <Text style={styles.channelName}>{video.channel}</Text>
                  </View>
                  {video.progress > 0 && (
                    <Text style={styles.progressText}>{video.progress}% watched</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Performance Analysis */}
        <View style={styles.analysisContainer}>
          <Text style={styles.sectionTitle}>Performance Analysis</Text>
          <View style={styles.analysisGrid}>
            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <CheckCircle2 size={20} color="#059669" />
                <Text style={styles.analysisTitle}>Strengths</Text>
              </View>
              {strengths.map((strength, index) => (
                <View key={index} style={styles.analysisItem}>
                  <View style={styles.analysisItemContent}>
                    <Text style={styles.analysisItemTitle}>{strength.topic}</Text>
                    <Text style={styles.analysisItemSubject}>{strength.subject}</Text>
                  </View>
                  <Text style={[styles.analysisScore, { color: '#059669' }]}>{strength.score}%</Text>
                </View>
              ))}
            </View>

            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <AlertTriangle size={20} color="#dc2626" />
                <Text style={styles.analysisTitle}>Areas to Improve</Text>
              </View>
              {weaknesses.map((weakness, index) => (
                <View key={index} style={styles.analysisItem}>
                  <View style={styles.analysisItemContent}>
                    <Text style={styles.analysisItemTitle}>{weakness.topic}</Text>
                    <Text style={styles.analysisItemSubject}>{weakness.subject}</Text>
                  </View>
                  <Text style={[styles.analysisScore, { color: '#dc2626' }]}>{weakness.score}%</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Today's Goal */}
        <View style={styles.goalContainer}>
          <Text style={styles.sectionTitle}>Today's Goal</Text>
          <View style={styles.goalCard}>
            <View style={styles.goalHeader}>
              <Target size={24} color="#1e40af" />
              <View style={styles.goalInfo}>
                <Text style={styles.goalTitle}>Complete 2 Topics</Text>
                <Text style={styles.goalSubtitle}>Engineering Mathematics & Thermodynamics</Text>
              </View>
            </View>
            <View style={styles.goalProgress}>
              <View style={styles.goalProgressBar}>
                <View style={[styles.goalProgressFill, { width: '50%' }]} />
              </View>
              <Text style={styles.goalProgressText}>1/2 Complete</Text>
            </View>
          </View>
        </View>

        {/* Upcoming Deadlines */}
        <View style={styles.deadlinesContainer}>
          <Text style={styles.sectionTitle}>Upcoming Deadlines</Text>
          <View style={styles.deadlinesCard}>
            {upcomingDeadlines.map((deadline, index) => (
              <View key={index} style={styles.deadlineItem}>
                <View style={styles.deadlineContent}>
                  <Text style={styles.deadlineTitle}>{deadline.title}</Text>
                  <Text style={styles.deadlineDate}>{deadline.date}</Text>
                </View>
                <View style={[
                  styles.deadlineIndicator,
                  { backgroundColor: deadline.urgent ? '#dc2626' : '#059669' }
                ]} />
              </View>
            ))}
          </View>
        </View>

        {/* Community Highlight */}
        <View style={styles.communityContainer}>
          <Text style={styles.sectionTitle}>Community Highlight</Text>
          <TouchableOpacity
            style={styles.communityCard}
            onPress={() => handleFeaturePress('/community')}
          >
            <View style={styles.communityHeader}>
              <Users size={20} color="#7c3aed" />
              <Text style={styles.communityTitle}>Join Study Group</Text>
            </View>
            <Text style={styles.communityDescription}>
              Connect with 2,500+ GATE aspirants. Share doubts, discuss solutions, and motivate each other!
            </Text>
            <View style={styles.communityStats}>
              <View style={styles.communityStat}>
                <Text style={styles.communityStatNumber}>2.5K</Text>
                <Text style={styles.communityStatLabel}>Members</Text>
              </View>
              <View style={styles.communityStat}>
                <Text style={styles.communityStatNumber}>150</Text>
                <Text style={styles.communityStatLabel}>Active Today</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  greeting: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  userName: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  notificationButton: {
    width: 40,
    height: 40,
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    backgroundColor: '#dc2626',
    borderRadius: 4,
  },
  profileButton: {
    width: 44,
    height: 44,
    backgroundColor: '#1e40af',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitial: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  progressCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#ffffff',
  },
  progressSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
    marginTop: 2,
  },
  daysLeft: {
    alignItems: 'center',
  },
  daysNumber: {
    fontSize: 24,
    fontFamily: 'RobotoSlab-Bold',
    color: '#ffffff',
  },
  daysLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#e0e7ff',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#1e3a8a',
    borderRadius: 4,
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#e0e7ff',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - 60) / 2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statIcon: {
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  actionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  featuresContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureCard: {
    width: (width - 60) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
  },
  videosContainer: {
    paddingLeft: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 20,
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1e40af',
  },
  videosScroll: {
    paddingRight: 20,
  },
  videoCard: {
    width: 200,
    marginRight: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  videoThumbnail: {
    position: 'relative',
    height: 120,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  playOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
    width: 40,
    height: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  durationText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  progressOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  videoProgress: {
    height: '100%',
    backgroundColor: '#1e40af',
  },
  videoInfo: {
    padding: 12,
  },
  videoTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 6,
    lineHeight: 18,
  },
  videoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  channelName: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginLeft: 4,
  },
  analysisContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  analysisGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  analysisCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  analysisTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  analysisItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  analysisItemContent: {
    flex: 1,
  },
  analysisItemTitle: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  analysisItemSubject: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  analysisScore: {
    fontSize: 12,
    fontFamily: 'RobotoSlab-Bold',
  },
  goalContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  goalCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  goalInfo: {
    marginLeft: 12,
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  goalSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  goalProgress: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalProgressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginRight: 12,
  },
  goalProgressFill: {
    height: '100%',
    backgroundColor: '#1e40af',
    borderRadius: 4,
  },
  goalProgressText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  deadlinesContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  deadlinesCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  deadlineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  deadlineContent: {
    flex: 1,
  },
  deadlineTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  deadlineDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  deadlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  communityContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  communityCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  communityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  communityTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 8,
  },
  communityDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  communityStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  communityStat: {
    alignItems: 'center',
  },
  communityStatNumber: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#7c3aed',
  },
  communityStatLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
});