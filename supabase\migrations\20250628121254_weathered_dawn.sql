/*
  # AI Tutor and User Interactions Schema
  
  1. Tables Created
    - ai_conversations: Chat conversations with AI tutor
    - ai_messages: Individual messages in conversations
    - doubt_submissions: User submitted doubts and questions
    - ai_study_plans: AI generated personalized study plans
    - ai_recommendations: AI recommendations for users
    - user_feedback: User feedback on AI responses
    - ai_analytics: Analytics on AI usage and effectiveness
    
  2. Features
    - Complete AI chat system with conversation history
    - Doubt submission and resolution tracking
    - AI-generated personalized study plans
    - Recommendation engine for content and study paths
    - Feedback system for AI improvement
    - Analytics for AI performance monitoring
*/

-- AI conversation sessions
CREATE TABLE IF NOT EXISTS ai_conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  title text,
  conversation_type text DEFAULT 'general' CHECK (conversation_type IN ('general', 'doubt', 'study-plan', 'test-help', 'concept-explanation')),
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  is_active boolean DEFAULT true,
  last_message_at timestamptz DEFAULT now(),
  message_count integer DEFAULT 0,
  ai_model_version text DEFAULT 'gemini-1.5',
  conversation_context jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Individual messages in AI conversations
CREATE TABLE IF NOT EXISTS ai_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid REFERENCES ai_conversations(id) ON DELETE CASCADE,
  sender_type text NOT NULL CHECK (sender_type IN ('user', 'ai')),
  message_text text NOT NULL,
  message_type text DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'formula', 'code', 'diagram')),
  attachments jsonb DEFAULT '[]',
  ai_confidence_score decimal(3,2), -- AI confidence in response (0-1)
  processing_time_ms integer, -- Time taken to generate AI response
  tokens_used integer, -- Tokens consumed for AI response
  message_metadata jsonb DEFAULT '{}',
  is_helpful boolean, -- User feedback on AI message
  user_rating integer CHECK (user_rating >= 1 AND user_rating <= 5),
  created_at timestamptz DEFAULT now()
);

-- User submitted doubts and questions
CREATE TABLE IF NOT EXISTS doubt_submissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  subtopic_id uuid REFERENCES subtopics(id),
  question_text text NOT NULL,
  question_type text DEFAULT 'conceptual' CHECK (question_type IN ('conceptual', 'numerical', 'application', 'derivation')),
  difficulty_level text DEFAULT 'Medium' CHECK (difficulty_level IN ('Easy', 'Medium', 'Hard')),
  attachments jsonb DEFAULT '[]',
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'resolved', 'escalated')),
  ai_response text,
  ai_response_quality integer CHECK (ai_response_quality >= 1 AND ai_response_quality <= 5),
  human_expert_response text,
  resolution_time_minutes integer,
  is_public boolean DEFAULT false, -- Allow others to see this doubt
  upvotes integer DEFAULT 0,
  tags text[] DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  resolved_at timestamptz
);

-- AI generated personalized study plans
CREATE TABLE IF NOT EXISTS ai_study_plans (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  plan_name text NOT NULL,
  plan_description text,
  plan_type text DEFAULT 'comprehensive' CHECK (plan_type IN ('comprehensive', 'weak-areas', 'revision', 'exam-focused', 'time-bound')),
  target_exam_date date,
  daily_study_hours integer DEFAULT 4,
  subjects_included uuid[] DEFAULT '{}',
  topics_sequence jsonb NOT NULL, -- Ordered sequence of topics with timelines
  difficulty_progression text DEFAULT 'gradual' CHECK (difficulty_progression IN ('easy-first', 'gradual', 'mixed', 'hard-first')),
  estimated_completion_days integer,
  ai_reasoning text, -- AI's explanation for the plan structure
  customization_factors jsonb DEFAULT '{}', -- Factors considered for personalization
  is_active boolean DEFAULT true,
  progress_percentage integer DEFAULT 0,
  adherence_score decimal(3,2) DEFAULT 0, -- How well user is following the plan
  last_updated_by_ai timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- AI recommendations for users
CREATE TABLE IF NOT EXISTS ai_recommendations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  recommendation_type text NOT NULL CHECK (recommendation_type IN ('study-topic', 'test', 'revision', 'break', 'resource', 'strategy')),
  title text NOT NULL,
  description text NOT NULL,
  priority text DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  confidence_score decimal(3,2) NOT NULL, -- AI confidence in recommendation
  reasoning text, -- AI's reasoning for the recommendation
  action_data jsonb DEFAULT '{}', -- Data needed to act on recommendation
  subject_id uuid REFERENCES subjects(id),
  topic_id uuid REFERENCES topics(id),
  expires_at timestamptz,
  is_viewed boolean DEFAULT false,
  is_accepted boolean DEFAULT false,
  is_dismissed boolean DEFAULT false,
  user_feedback text,
  effectiveness_score integer CHECK (effectiveness_score >= 1 AND effectiveness_score <= 5),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User feedback on AI responses and interactions
CREATE TABLE IF NOT EXISTS user_feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  feedback_type text NOT NULL CHECK (feedback_type IN ('ai-response', 'study-plan', 'recommendation', 'general')),
  reference_id uuid, -- ID of the item being rated (message, plan, etc.)
  rating integer NOT NULL CHECK (rating >= 1 AND rating <= 5),
  feedback_text text,
  improvement_suggestions text,
  categories text[] DEFAULT '{}', -- accuracy, helpfulness, clarity, etc.
  is_anonymous boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- AI analytics and performance metrics
CREATE TABLE IF NOT EXISTS ai_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_type text NOT NULL CHECK (metric_type IN ('usage', 'performance', 'satisfaction', 'effectiveness')),
  metric_name text NOT NULL,
  metric_value decimal(10,4) NOT NULL,
  metric_unit text,
  dimensions jsonb DEFAULT '{}', -- Additional dimensions for the metric
  time_period text DEFAULT 'daily' CHECK (time_period IN ('hourly', 'daily', 'weekly', 'monthly')),
  recorded_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE doubt_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_study_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_conversations
CREATE POLICY "Users can manage own AI conversations"
  ON ai_conversations
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for ai_messages
CREATE POLICY "Users can read own AI messages"
  ON ai_messages
  FOR SELECT
  TO authenticated
  USING (auth.uid() = (SELECT user_id FROM ai_conversations WHERE id = conversation_id));

CREATE POLICY "Users can insert own AI messages"
  ON ai_messages
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = (SELECT user_id FROM ai_conversations WHERE id = conversation_id));

CREATE POLICY "Users can update own AI messages"
  ON ai_messages
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = (SELECT user_id FROM ai_conversations WHERE id = conversation_id));

-- RLS Policies for doubt_submissions
CREATE POLICY "Users can manage own doubts"
  ON doubt_submissions
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read public doubts"
  ON doubt_submissions
  FOR SELECT
  TO authenticated
  USING (is_public = true);

-- RLS Policies for ai_study_plans
CREATE POLICY "Users can manage own AI study plans"
  ON ai_study_plans
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for ai_recommendations
CREATE POLICY "Users can manage own AI recommendations"
  ON ai_recommendations
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for user_feedback
CREATE POLICY "Users can manage own feedback"
  ON user_feedback
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for ai_analytics (admin only - no policies for regular users)
CREATE POLICY "No access to AI analytics"
  ON ai_analytics
  FOR ALL
  TO authenticated
  USING (false);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_id ON ai_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_active ON ai_conversations(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_last_message ON ai_conversations(last_message_at);
CREATE INDEX IF NOT EXISTS idx_ai_messages_conversation_id ON ai_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_ai_messages_created_at ON ai_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_doubt_submissions_user_id ON doubt_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_doubt_submissions_status ON doubt_submissions(status);
CREATE INDEX IF NOT EXISTS idx_doubt_submissions_subject ON doubt_submissions(subject_id);
CREATE INDEX IF NOT EXISTS idx_doubt_submissions_public ON doubt_submissions(is_public);
CREATE INDEX IF NOT EXISTS idx_ai_study_plans_user_id ON ai_study_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_study_plans_active ON ai_study_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_user_id ON ai_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_viewed ON ai_recommendations(is_viewed);
CREATE INDEX IF NOT EXISTS idx_user_feedback_type ON user_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_user_feedback_reference ON user_feedback(reference_id);
CREATE INDEX IF NOT EXISTS idx_ai_analytics_metric ON ai_analytics(metric_type, metric_name);
CREATE INDEX IF NOT EXISTS idx_ai_analytics_time ON ai_analytics(recorded_at);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_ai_conversations_updated_at
  BEFORE UPDATE ON ai_conversations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_doubt_submissions_updated_at
  BEFORE UPDATE ON doubt_submissions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_study_plans_updated_at
  BEFORE UPDATE ON ai_study_plans
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_recommendations_updated_at
  BEFORE UPDATE ON ai_recommendations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();