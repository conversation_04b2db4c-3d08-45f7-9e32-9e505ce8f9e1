import React, { createContext, useContext, useState, useEffect } from 'react';
import { router } from 'expo-router';

interface User {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  college?: string;
  graduationYear?: string;
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (userData: any) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // In a real app, you would check stored tokens/session
      // For demo purposes, we'll simulate this
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // Simulate API call
      const userData = {
        id: '1',
        email,
        fullName: 'John Doe',
        phone: '+91 **********',
        college: 'IIT Delhi',
        graduationYear: '2025',
      };
      
      setUser(userData);
      router.replace('/(tabs)');
    } catch (error) {
      throw new Error('Invalid credentials');
    }
  };

  const signUp = async (userData: any) => {
    try {
      // Simulate API call
      const newUser = {
        id: Date.now().toString(),
        ...userData,
      };
      
      setUser(newUser);
      router.replace('/(tabs)');
    } catch (error) {
      throw new Error('Failed to create account');
    }
  };

  const signOut = async () => {
    try {
      setUser(null);
      router.replace('/welcome');
    } catch (error) {
      throw new Error('Failed to sign out');
    }
  };

  const value = {
    user,
    isLoading,
    signIn,
    signUp,
    signOut,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}