import React, { createContext, useContext, useState, useEffect } from 'react';
import { router } from 'expo-router';
import { supabase, signIn as supabaseSignIn, signUp as supabaseSignUp, signOut as supabaseSignOut, getCurrentUser, getUserProfile, createUserProfile } from '@/lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface User {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  college?: string;
  graduationYear?: number;
  avatar?: string;
  branch?: string;
  gateExamYear?: number;
  targetScore?: number;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (userData: {
    fullName: string;
    email: string;
    phone?: string;
    password: string;
    college?: string;
    graduationYear?: string;
  }) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated
    checkAuthStatus();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        await loadUserData(session.user);
      } else {
        setUser(null);
      }
      setIsLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const currentUser = await getCurrentUser();
      if (currentUser) {
        await loadUserData(currentUser);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserData = async (supabaseUser: SupabaseUser) => {
    try {
      const profile = await getUserProfile(supabaseUser.id);

      const userData: User = {
        id: supabaseUser.id,
        email: supabaseUser.email || '',
        fullName: supabaseUser.user_metadata?.full_name || profile?.college_name || 'User',
        phone: supabaseUser.user_metadata?.phone || profile?.phone,
        college: supabaseUser.user_metadata?.college_name || profile?.college_name,
        graduationYear: supabaseUser.user_metadata?.graduation_year || profile?.graduation_year,
        avatar: profile?.avatar_url,
        branch: profile?.branch,
        gateExamYear: profile?.gate_exam_year,
        targetScore: profile?.target_score,
      };

      setUser(userData);
    } catch (error) {
      console.error('Error loading user data:', error);
      // Set basic user data even if profile loading fails
      setUser({
        id: supabaseUser.id,
        email: supabaseUser.email || '',
        fullName: supabaseUser.user_metadata?.full_name || 'User',
      });
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const { user: supabaseUser } = await supabaseSignIn(email, password);

      if (supabaseUser) {
        await loadUserData(supabaseUser);
        router.replace('/(tabs)');
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      throw new Error(error.message || 'Invalid credentials');
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (userData: {
    fullName: string;
    email: string;
    phone?: string;
    password: string;
    college?: string;
    graduationYear?: string;
  }) => {
    try {
      setIsLoading(true);
      const { user: supabaseUser } = await supabaseSignUp(userData.email, userData.password, {
        fullName: userData.fullName,
        phone: userData.phone,
        college: userData.college,
        graduationYear: userData.graduationYear,
      });

      if (supabaseUser) {
        // Create user profile
        try {
          await createUserProfile(supabaseUser.id, {
            phone: userData.phone,
            college_name: userData.college,
            graduation_year: userData.graduationYear ? parseInt(userData.graduationYear) : undefined,
            branch: 'Mechanical Engineering',
            gate_exam_year: 2025,
            target_score: 80,
          });
        } catch (profileError) {
          console.error('Error creating profile:', profileError);
          // Continue even if profile creation fails
        }

        await loadUserData(supabaseUser);
        router.replace('/(tabs)');
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      throw new Error(error.message || 'Failed to create account');
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await supabaseSignOut();
      setUser(null);
      router.replace('/welcome');
    } catch (error: any) {
      console.error('Sign out error:', error);
      throw new Error(error.message || 'Failed to sign out');
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await getCurrentUser();
      if (currentUser) {
        await loadUserData(currentUser);
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  const value = {
    user,
    isLoading,
    signIn,
    signUp,
    signOut,
    refreshUser,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}