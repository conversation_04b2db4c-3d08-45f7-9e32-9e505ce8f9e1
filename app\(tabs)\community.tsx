import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Image, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MessageCircle, Users, Plus, Search, Heart, MessageSquare, Share, BookOpen, Trophy, Star, Clock, ChevronRight, Send } from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function CommunityScreen() {
  const [activeTab, setActiveTab] = useState('discussions');
  const [searchQuery, setSearchQuery] = useState('');

  const tabs = [
    { id: 'discussions', name: 'Discussions', icon: MessageCircle },
    { id: 'study-groups', name: 'Study Groups', icon: Users },
    { id: 'achievements', name: 'Achievements', icon: Trophy },
  ];

  const discussions = [
    {
      id: 1,
      title: 'Best strategy for Linear Algebra preparation?',
      author: '<PERSON><PERSON>',
      avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      timeAgo: '2 hours ago',
      category: 'Engineering Mathematics',
      replies: 24,
      likes: 18,
      isLiked: false,
      preview: 'I\'m struggling with eigenvalues and eigenvectors. Any tips on how to approach these problems systematically?',
      tags: ['Linear Algebra', 'Mathematics', 'Tips']
    },
    {
      id: 2,
      title: 'Thermodynamics First Law - Common Mistakes',
      author: 'Priya Singh',
      avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      timeAgo: '4 hours ago',
      category: 'Thermodynamics',
      replies: 31,
      likes: 42,
      isLiked: true,
      preview: 'Here are the most common mistakes I\'ve seen students make while solving First Law problems...',
      tags: ['Thermodynamics', 'First Law', 'Common Mistakes']
    },
    {
      id: 3,
      title: 'GATE 2024 vs 2025 - Pattern Changes?',
      author: 'Amit Kumar',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      timeAgo: '6 hours ago',
      category: 'General',
      replies: 15,
      likes: 28,
      isLiked: false,
      preview: 'Has anyone noticed any significant changes in the GATE 2025 pattern compared to previous years?',
      tags: ['GATE 2025', 'Pattern', 'Changes']
    }
  ];

  const studyGroups = [
    {
      id: 1,
      name: 'GATE ME 2025 - Delhi NCR',
      description: 'Study group for GATE Mechanical aspirants in Delhi NCR region',
      members: 156,
      avatar: 'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      isJoined: true,
      lastActivity: '2 hours ago',
      category: 'Regional',
      activeMembers: 23
    },
    {
      id: 2,
      name: 'Thermodynamics Masters',
      description: 'Dedicated group for mastering thermodynamics concepts',
      members: 89,
      avatar: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      isJoined: false,
      lastActivity: '1 hour ago',
      category: 'Subject-wise',
      activeMembers: 12
    },
    {
      id: 3,
      name: 'Mock Test Discussion Hub',
      description: 'Discuss mock test questions and solutions',
      members: 234,
      avatar: 'https://images.pexels.com/photos/256541/pexels-photo-256541.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      isJoined: true,
      lastActivity: '30 minutes ago',
      category: 'Test Prep',
      activeMembers: 45
    }
  ];

  const achievements = [
    {
      id: 1,
      user: 'Sneha Patel',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      achievement: 'Completed 50 Mock Tests',
      timeAgo: '1 hour ago',
      type: 'milestone',
      icon: Trophy,
      color: '#f59e0b'
    },
    {
      id: 2,
      user: 'Vikash Gupta',
      avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      achievement: '30-Day Study Streak',
      timeAgo: '3 hours ago',
      type: 'streak',
      icon: Star,
      color: '#dc2626'
    },
    {
      id: 3,
      user: 'Ananya Roy',
      avatar: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      achievement: 'Top Scorer - Weekly Challenge',
      timeAgo: '5 hours ago',
      type: 'competition',
      icon: Trophy,
      color: '#059669'
    }
  ];

  const trendingTopics = [
    { name: 'Linear Algebra', posts: 45, trend: '+12%' },
    { name: 'Heat Transfer', posts: 38, trend: '+8%' },
    { name: 'GATE 2025 Strategy', posts: 52, trend: '+15%' },
    { name: 'Mock Test Tips', posts: 29, trend: '+6%' }
  ];

  const communityStats = {
    totalMembers: 15247,
    activeToday: 1856,
    totalPosts: 8934,
    totalGroups: 156
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Community</Text>
        <Text style={styles.subtitle}>Connect with fellow GATE aspirants</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[styles.tab, activeTab === tab.id && styles.activeTab]}
            onPress={() => setActiveTab(tab.id)}
          >
            <tab.icon 
              size={18} 
              color={activeTab === tab.id ? '#1e40af' : '#6b7280'} 
            />
            <Text 
              style={[
                styles.tabText, 
                activeTab === tab.id && styles.activeTabText
              ]}
            >
              {tab.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Search size={20} color="#6b7280" />
          <TextInput
            style={styles.searchInput}
            placeholder={`Search ${activeTab.replace('-', ' ')}...`}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9ca3af"
          />
        </View>
        <TouchableOpacity style={styles.createButton}>
          <Plus size={20} color="#ffffff" />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {activeTab === 'discussions' && (
          <>
            {/* Community Stats */}
            <View style={styles.statsContainer}>
              <View style={styles.statsGrid}>
                <View style={styles.statCard}>
                  <Users size={20} color="#1e40af" />
                  <Text style={styles.statNumber}>{(communityStats.totalMembers / 1000).toFixed(1)}K</Text>
                  <Text style={styles.statLabel}>Members</Text>
                </View>
                <View style={styles.statCard}>
                  <MessageCircle size={20} color="#059669" />
                  <Text style={styles.statNumber}>{(communityStats.totalPosts / 1000).toFixed(1)}K</Text>
                  <Text style={styles.statLabel}>Posts</Text>
                </View>
                <View style={styles.statCard}>
                  <Clock size={20} color="#ea580c" />
                  <Text style={styles.statNumber}>{(communityStats.activeToday / 1000).toFixed(1)}K</Text>
                  <Text style={styles.statLabel}>Active Today</Text>
                </View>
              </View>
            </View>

            {/* Trending Topics */}
            <View style={styles.trendingContainer}>
              <Text style={styles.sectionTitle}>Trending Topics</Text>
              <View style={styles.trendingCard}>
                {trendingTopics.map((topic, index) => (
                  <TouchableOpacity key={index} style={styles.trendingItem}>
                    <View style={styles.trendingInfo}>
                      <Text style={styles.trendingName}>{topic.name}</Text>
                      <Text style={styles.trendingPosts}>{topic.posts} posts</Text>
                    </View>
                    <View style={styles.trendingBadge}>
                      <Text style={styles.trendingText}>{topic.trend}</Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Discussions */}
            <View style={styles.discussionsContainer}>
              <Text style={styles.sectionTitle}>Recent Discussions</Text>
              {discussions.map((discussion) => (
                <TouchableOpacity key={discussion.id} style={styles.discussionCard}>
                  <View style={styles.discussionHeader}>
                    <Image source={{ uri: discussion.avatar }} style={styles.authorAvatar} />
                    <View style={styles.discussionMeta}>
                      <Text style={styles.authorName}>{discussion.author}</Text>
                      <Text style={styles.timeAgo}>{discussion.timeAgo}</Text>
                    </View>
                    <View style={styles.categoryBadge}>
                      <Text style={styles.categoryText}>{discussion.category}</Text>
                    </View>
                  </View>
                  
                  <Text style={styles.discussionTitle}>{discussion.title}</Text>
                  <Text style={styles.discussionPreview}>{discussion.preview}</Text>
                  
                  <View style={styles.discussionTags}>
                    {discussion.tags.map((tag, index) => (
                      <View key={index} style={styles.tag}>
                        <Text style={styles.tagText}>{tag}</Text>
                      </View>
                    ))}
                  </View>
                  
                  <View style={styles.discussionFooter}>
                    <View style={styles.discussionStats}>
                      <TouchableOpacity style={styles.statButton}>
                        <Heart 
                          size={16} 
                          color={discussion.isLiked ? "#dc2626" : "#6b7280"} 
                          fill={discussion.isLiked ? "#dc2626" : "transparent"}
                        />
                        <Text style={styles.statText}>{discussion.likes}</Text>
                      </TouchableOpacity>
                      <TouchableOpacity style={styles.statButton}>
                        <MessageSquare size={16} color="#6b7280" />
                        <Text style={styles.statText}>{discussion.replies}</Text>
                      </TouchableOpacity>
                      <TouchableOpacity style={styles.statButton}>
                        <Share size={16} color="#6b7280" />
                      </TouchableOpacity>
                    </View>
                    <ChevronRight size={16} color="#9ca3af" />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </>
        )}

        {activeTab === 'study-groups' && (
          <>
            {/* Study Groups */}
            <View style={styles.groupsContainer}>
              <Text style={styles.sectionTitle}>Study Groups</Text>
              {studyGroups.map((group) => (
                <TouchableOpacity key={group.id} style={styles.groupCard}>
                  <Image source={{ uri: group.avatar }} style={styles.groupAvatar} />
                  <View style={styles.groupInfo}>
                    <Text style={styles.groupName}>{group.name}</Text>
                    <Text style={styles.groupDescription}>{group.description}</Text>
                    <View style={styles.groupMeta}>
                      <View style={styles.groupStat}>
                        <Users size={14} color="#6b7280" />
                        <Text style={styles.groupStatText}>{group.members} members</Text>
                      </View>
                      <View style={styles.groupStat}>
                        <Clock size={14} color="#6b7280" />
                        <Text style={styles.groupStatText}>{group.lastActivity}</Text>
                      </View>
                    </View>
                    <View style={styles.groupFooter}>
                      <View style={styles.categoryBadge}>
                        <Text style={styles.categoryText}>{group.category}</Text>
                      </View>
                      <Text style={styles.activeMembers}>{group.activeMembers} active now</Text>
                    </View>
                  </View>
                  <TouchableOpacity 
                    style={[
                      styles.joinButton,
                      group.isJoined && styles.joinedButton
                    ]}
                  >
                    <Text 
                      style={[
                        styles.joinButtonText,
                        group.isJoined && styles.joinedButtonText
                      ]}
                    >
                      {group.isJoined ? 'Joined' : 'Join'}
                    </Text>
                  </TouchableOpacity>
                </TouchableOpacity>
              ))}
            </View>

            {/* Create Group CTA */}
            <View style={styles.createGroupContainer}>
              <Text style={styles.sectionTitle}>Create Your Own Group</Text>
              <TouchableOpacity style={styles.createGroupCard}>
                <Plus size={24} color="#1e40af" />
                <Text style={styles.createGroupTitle}>Start a Study Group</Text>
                <Text style={styles.createGroupDescription}>
                  Connect with like-minded students and study together
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {activeTab === 'achievements' && (
          <>
            {/* Recent Achievements */}
            <View style={styles.achievementsContainer}>
              <Text style={styles.sectionTitle}>Recent Achievements</Text>
              {achievements.map((achievement) => (
                <View key={achievement.id} style={styles.achievementCard}>
                  <Image source={{ uri: achievement.avatar }} style={styles.achievementAvatar} />
                  <View style={styles.achievementInfo}>
                    <Text style={styles.achievementUser}>{achievement.user}</Text>
                    <Text style={styles.achievementTitle}>{achievement.achievement}</Text>
                    <Text style={styles.achievementTime}>{achievement.timeAgo}</Text>
                  </View>
                  <View style={[styles.achievementIcon, { backgroundColor: achievement.color + '15' }]}>
                    <achievement.icon size={20} color={achievement.color} />
                  </View>
                </View>
              ))}
            </View>

            {/* Achievement Categories */}
            <View style={styles.categoriesContainer}>
              <Text style={styles.sectionTitle}>Achievement Categories</Text>
              <View style={styles.categoriesGrid}>
                <TouchableOpacity style={styles.categoryCard}>
                  <Trophy size={24} color="#f59e0b" />
                  <Text style={styles.categoryCardTitle}>Milestones</Text>
                  <Text style={styles.categoryCardCount}>24 achievements</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.categoryCard}>
                  <Star size={24} color="#dc2626" />
                  <Text style={styles.categoryCardTitle}>Streaks</Text>
                  <Text style={styles.categoryCardCount}>12 achievements</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.categoryCard}>
                  <BookOpen size={24} color="#1e40af" />
                  <Text style={styles.categoryCardTitle}>Study</Text>
                  <Text style={styles.categoryCardCount}>18 achievements</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.categoryCard}>
                  <Users size={24} color="#059669" />
                  <Text style={styles.categoryCardTitle}>Community</Text>
                  <Text style={styles.categoryCardCount}>8 achievements</Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab}>
        <Send size={24} color="#ffffff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    gap: 6,
  },
  activeTab: {
    backgroundColor: '#eff6ff',
    borderWidth: 1,
    borderColor: '#dbeafe',
  },
  tabText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activeTabText: {
    color: '#1e40af',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 16,
    gap: 12,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  createButton: {
    width: 48,
    height: 48,
    backgroundColor: '#1e40af',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
  trendingContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  trendingCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  trendingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  trendingInfo: {
    flex: 1,
  },
  trendingName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  trendingPosts: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  trendingBadge: {
    backgroundColor: '#ecfdf5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trendingText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
  discussionsContainer: {
    marginBottom: 24,
  },
  discussionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  discussionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  authorAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  discussionMeta: {
    flex: 1,
  },
  authorName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  timeAgo: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  categoryBadge: {
    backgroundColor: '#eff6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#1e40af',
  },
  discussionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  discussionPreview: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  discussionTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  tag: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
  },
  discussionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  discussionStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  groupsContainer: {
    marginBottom: 24,
  },
  groupCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  groupAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  groupDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  groupMeta: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 8,
  },
  groupStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  groupStatText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  groupFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activeMembers: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#059669',
  },
  joinButton: {
    backgroundColor: '#1e40af',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    marginLeft: 12,
  },
  joinedButton: {
    backgroundColor: '#f3f4f6',
  },
  joinButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  joinedButtonText: {
    color: '#6b7280',
  },
  createGroupContainer: {
    marginBottom: 24,
  },
  createGroupCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  createGroupTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 12,
    marginBottom: 8,
  },
  createGroupDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
  },
  achievementsContainer: {
    marginBottom: 24,
  },
  achievementCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  achievementAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementUser: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  achievementTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  achievementTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
    marginTop: 2,
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  categoriesContainer: {
    marginBottom: 24,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    width: (width - 64) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryCardTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  categoryCardCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    backgroundColor: '#1e40af',
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});