/*
  # Syllabus and Subject Management Schema
  
  1. Tables Created
    - subjects: GATE syllabus subjects
    - topics: Main topics within each subject
    - subtopics: Detailed subtopics for comprehensive coverage
    - user_subject_progress: Track user progress per subject
    - user_topic_progress: Track user progress per topic
    - user_subtopic_progress: Track detailed progress per subtopic
    
  2. Features
    - Complete GATE 2025 syllabus structure
    - Hierarchical organization (Subject → Topic → Subtopic)
    - Progress tracking at all levels
    - Difficulty levels and estimated study hours
    - Completion status and scoring
*/

-- Subjects table (Engineering Mathematics, Thermodynamics, etc.)
CREATE TABLE IF NOT EXISTS subjects (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  code text UNIQUE NOT NULL,
  section text NOT NULL,
  description text,
  color text DEFAULT '#1e40af',
  bg_color text DEFAULT '#eff6ff',
  estimated_hours integer DEFAULT 40,
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  weightage_percentage integer DEFAULT 15,
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Topics table (Linear Algebra, First Law of Thermodynamics, etc.)
CREATE TABLE IF NOT EXISTS topics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  subject_id uuid REFERENCES subjects(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  estimated_hours integer DEFAULT 8,
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  weightage_percentage integer DEFAULT 20,
  prerequisites text[] DEFAULT '{}',
  learning_objectives text[] DEFAULT '{}',
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Subtopics table (Matrix Operations, Eigenvalues, etc.)
CREATE TABLE IF NOT EXISTS subtopics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  topic_id uuid REFERENCES topics(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  estimated_hours integer DEFAULT 2,
  difficulty text DEFAULT 'Medium' CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  content_type text DEFAULT 'theory' CHECK (content_type IN ('theory', 'numerical', 'conceptual', 'application')),
  key_formulas text[] DEFAULT '{}',
  important_points text[] DEFAULT '{}',
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User progress tracking for subjects
CREATE TABLE IF NOT EXISTS user_subject_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  subject_id uuid REFERENCES subjects(id) ON DELETE CASCADE,
  completion_percentage integer DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  time_spent_minutes integer DEFAULT 0,
  last_studied_at timestamptz,
  average_score integer DEFAULT 0,
  total_tests_attempted integer DEFAULT 0,
  best_score integer DEFAULT 0,
  study_streak_days integer DEFAULT 0,
  is_favorite boolean DEFAULT false,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, subject_id)
);

-- User progress tracking for topics
CREATE TABLE IF NOT EXISTS user_topic_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  topic_id uuid REFERENCES topics(id) ON DELETE CASCADE,
  subject_id uuid REFERENCES subjects(id) ON DELETE CASCADE,
  is_completed boolean DEFAULT false,
  completion_percentage integer DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  time_spent_minutes integer DEFAULT 0,
  last_studied_at timestamptz,
  average_score integer DEFAULT 0,
  total_attempts integer DEFAULT 0,
  best_score integer DEFAULT 0,
  confidence_level integer DEFAULT 1 CHECK (confidence_level >= 1 AND confidence_level <= 5),
  notes text,
  bookmarked boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, topic_id)
);

-- User progress tracking for subtopics
CREATE TABLE IF NOT EXISTS user_subtopic_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  subtopic_id uuid REFERENCES subtopics(id) ON DELETE CASCADE,
  topic_id uuid REFERENCES topics(id) ON DELETE CASCADE,
  is_completed boolean DEFAULT false,
  completion_date timestamptz,
  time_spent_minutes integer DEFAULT 0,
  last_studied_at timestamptz,
  understanding_level integer DEFAULT 1 CHECK (understanding_level >= 1 AND understanding_level <= 5),
  revision_count integer DEFAULT 0,
  last_revised_at timestamptz,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, subtopic_id)
);

-- Enable Row Level Security
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE subtopics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subject_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_topic_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subtopic_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies for subjects (public read access)
CREATE POLICY "Anyone can read subjects"
  ON subjects
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for topics (public read access)
CREATE POLICY "Anyone can read topics"
  ON topics
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for subtopics (public read access)
CREATE POLICY "Anyone can read subtopics"
  ON subtopics
  FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for user_subject_progress
CREATE POLICY "Users can read own subject progress"
  ON user_subject_progress
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subject progress"
  ON user_subject_progress
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subject progress"
  ON user_subject_progress
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for user_topic_progress
CREATE POLICY "Users can read own topic progress"
  ON user_topic_progress
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own topic progress"
  ON user_topic_progress
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own topic progress"
  ON user_topic_progress
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for user_subtopic_progress
CREATE POLICY "Users can read own subtopic progress"
  ON user_subtopic_progress
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subtopic progress"
  ON user_subtopic_progress
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subtopic progress"
  ON user_subtopic_progress
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_subjects_code ON subjects(code);
CREATE INDEX IF NOT EXISTS idx_subjects_section ON subjects(section);
CREATE INDEX IF NOT EXISTS idx_topics_subject_id ON topics(subject_id);
CREATE INDEX IF NOT EXISTS idx_topics_display_order ON topics(display_order);
CREATE INDEX IF NOT EXISTS idx_subtopics_topic_id ON subtopics(topic_id);
CREATE INDEX IF NOT EXISTS idx_subtopics_display_order ON subtopics(display_order);
CREATE INDEX IF NOT EXISTS idx_user_subject_progress_user_id ON user_subject_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subject_progress_subject_id ON user_subject_progress(subject_id);
CREATE INDEX IF NOT EXISTS idx_user_topic_progress_user_id ON user_topic_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_topic_progress_topic_id ON user_topic_progress(topic_id);
CREATE INDEX IF NOT EXISTS idx_user_subtopic_progress_user_id ON user_subtopic_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subtopic_progress_subtopic_id ON user_subtopic_progress(subtopic_id);

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_subjects_updated_at
  BEFORE UPDATE ON subjects
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_topics_updated_at
  BEFORE UPDATE ON topics
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subtopics_updated_at
  BEFORE UPDATE ON subtopics
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subject_progress_updated_at
  BEFORE UPDATE ON user_subject_progress
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_topic_progress_updated_at
  BEFORE UPDATE ON user_topic_progress
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subtopic_progress_updated_at
  BEFORE UPDATE ON user_subtopic_progress
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();