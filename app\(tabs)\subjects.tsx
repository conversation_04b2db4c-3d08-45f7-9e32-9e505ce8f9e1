import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Search, ChevronRight, Book, Clock, CircleCheck as CheckCircle2, Play, Youtube, FileText, Zap, BookOpen, Target } from 'lucide-react-native';

export default function SubjectsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSection, setSelectedSection] = useState('all');
  
  const sections = [
    { id: 'all', name: 'All Sections' },
    { id: 'section1', name: 'Engineering Mathematics' },
    { id: 'section2', name: 'Applied Mechanics & Design' },
    { id: 'section3', name: 'Fluid Mechanics & Thermal Sciences' },
    { id: 'section4', name: 'Materials & Manufacturing' },
  ];

  const gateSubjects = [
    {
      id: 'engineering-mathematics',
      name: 'Engineering Mathematics',
      section: 'Section 1: Engineering Mathematics',
      description: 'Linear Algebra, Calculus, Differential Equations, Complex Variables, Probability and Statistics',
      color: '#1e40af',
      bgColor: '#eff6ff',
      estimatedHours: 80,
      difficulty: 'Medium',
      completed: 45,
      total: 65,
      topics: [
        { name: 'Linear Algebra', completed: true },
        { name: 'Calculus', completed: true },
        { name: 'Differential Equations', completed: false },
        { name: 'Complex Variables', completed: false },
        { name: 'Probability and Statistics', completed: false }
      ]
    },
    {
      id: 'applied-mechanics',
      name: 'Applied Mechanics',
      section: 'Section 2: Applied Mechanics and Design',
      description: 'Engineering Mechanics, Strength of Materials, Theory of Machines, Vibrations',
      color: '#dc2626',
      bgColor: '#fef2f2',
      estimatedHours: 100,
      difficulty: 'Hard',
      completed: 32,
      total: 58,
      topics: [
        { name: 'Engineering Mechanics', completed: true },
        { name: 'Strength of Materials', completed: false },
        { name: 'Theory of Machines', completed: false },
        { name: 'Vibrations', completed: false }
      ]
    },
    {
      id: 'fluid-mechanics',
      name: 'Fluid Mechanics',
      section: 'Section 3: Fluid Mechanics and Thermal Sciences',
      description: 'Fluid Mechanics, Heat Transfer, Thermodynamics, IC Engines, Refrigeration',
      color: '#0891b2',
      bgColor: '#ecfeff',
      estimatedHours: 90,
      difficulty: 'Medium',
      completed: 28,
      total: 52,
      topics: [
        { name: 'Fluid Mechanics', completed: true },
        { name: 'Heat Transfer', completed: false },
        { name: 'Thermodynamics', completed: true },
        { name: 'IC Engines', completed: false }
      ]
    },
    {
      id: 'manufacturing',
      name: 'Manufacturing Engineering',
      section: 'Section 4: Materials, Manufacturing and Industrial Engineering',
      description: 'Manufacturing Processes, Metrology, Materials Science, Industrial Engineering',
      color: '#059669',
      bgColor: '#ecfdf5',
      estimatedHours: 85,
      difficulty: 'Medium',
      completed: 37,
      total: 48,
      topics: [
        { name: 'Manufacturing Processes', completed: true },
        { name: 'Metrology', completed: false },
        { name: 'Materials Science', completed: true },
        { name: 'Industrial Engineering', completed: false }
      ]
    }
  ];

  const filteredSubjects = gateSubjects.filter(subject => {
    const matchesSearch = subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         subject.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (selectedSection === 'all') return matchesSearch;
    
    const sectionMap: { [key: string]: string } = {
      'section1': 'Section 1: Engineering Mathematics',
      'section2': 'Section 2: Applied Mechanics and Design',
      'section3': 'Section 3: Fluid Mechanics and Thermal Sciences',
      'section4': 'Section 4: Materials, Manufacturing and Industrial Engineering',
    };
    
    return matchesSearch && subject.section === sectionMap[selectedSection];
  });

  const getProgressPercentage = (completed: number, total: number) => {
    return Math.round((completed / total) * 100);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return '#059669';
      case 'Medium': return '#ea580c';
      case 'Hard': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const syllabusStats = {
    totalSubjects: gateSubjects.length,
    totalTopics: gateSubjects.reduce((sum, subject) => sum + subject.topics.length, 0),
    totalSubtopics: gateSubjects.reduce((sum, subject) => sum + subject.total, 0),
    completedSubtopics: gateSubjects.reduce((sum, subject) => sum + subject.completed, 0),
    overallProgress: Math.round((gateSubjects.reduce((sum, subject) => sum + subject.completed, 0) / gateSubjects.reduce((sum, subject) => sum + subject.total, 0)) * 100)
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>GATE Mechanical Syllabus</Text>
        <Text style={styles.subtitle}>Complete syllabus as per GATE 2025</Text>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Search size={20} color="#6b7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search subjects or topics..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9ca3af"
          />
        </View>
      </View>

      {/* Section Filter */}
      <View style={styles.sectionFilterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.sectionFilter}>
          {sections.map((section) => (
            <TouchableOpacity
              key={section.id}
              style={[
                styles.sectionButton,
                selectedSection === section.id && styles.activeSectionButton
              ]}
              onPress={() => setSelectedSection(section.id)}
            >
              <Text
                style={[
                  styles.sectionButtonText,
                  selectedSection === section.id && styles.activeSectionButtonText
                ]}
              >
                {section.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {/* Overall Progress */}
        <View style={styles.overallProgressContainer}>
          <Text style={styles.sectionTitle}>Overall Progress</Text>
          <View style={styles.overallProgressCard}>
            <View style={styles.progressStats}>
              <View style={styles.progressStat}>
                <Text style={styles.progressNumber}>{syllabusStats.completedSubtopics}</Text>
                <Text style={styles.progressLabel}>Topics Completed</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressNumber}>{syllabusStats.totalSubtopics}</Text>
                <Text style={styles.progressLabel}>Total Topics</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressNumber}>{syllabusStats.overallProgress}%</Text>
                <Text style={styles.progressLabel}>Overall Progress</Text>
              </View>
            </View>
            <View style={styles.overallProgressBar}>
              <View 
                style={[
                  styles.overallProgressFill, 
                  { width: `${syllabusStats.overallProgress}%` }
                ]} 
              />
            </View>
          </View>
        </View>

        {/* Subjects List */}
        <View style={styles.subjectsContainer}>
          <Text style={styles.sectionTitle}>
            {selectedSection === 'all' ? 'All Subjects' : sections.find(s => s.id === selectedSection)?.name}
            <Text style={styles.subjectCount}> ({filteredSubjects.length})</Text>
          </Text>
          
          {filteredSubjects.map((subject) => {
            const progressPercentage = getProgressPercentage(subject.completed, subject.total);
            const completedTopics = subject.topics.filter(topic => topic.completed).length;
            
            return (
              <TouchableOpacity key={subject.id} style={styles.subjectCard}>
                <View style={styles.subjectHeader}>
                  <View style={[styles.subjectIcon, { backgroundColor: subject.bgColor }]}>
                    <Book size={24} color={subject.color} />
                  </View>
                  <View style={styles.subjectInfo}>
                    <Text style={styles.subjectName}>{subject.name}</Text>
                    <Text style={styles.subjectSection}>{subject.section}</Text>
                    <Text style={styles.subjectDescription}>{subject.description}</Text>
                    <View style={styles.subjectMeta}>
                      <View style={styles.metaItem}>
                        <Clock size={14} color="#6b7280" />
                        <Text style={styles.metaText}>{subject.estimatedHours}h</Text>
                      </View>
                      <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(subject.difficulty) + '15' }]}>
                        <Text style={[styles.difficultyText, { color: getDifficultyColor(subject.difficulty) }]}>
                          {subject.difficulty}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <ChevronRight size={20} color="#9ca3af" />
                </View>

                <View style={styles.progressContainer}>
                  <View style={styles.progressInfo}>
                    <Text style={styles.progressLabel}>
                      {completedTopics}/{subject.topics.length} topics • {subject.completed}/{subject.total} subtopics
                    </Text>
                    <Text style={styles.progressPercentage}>{progressPercentage}%</Text>
                  </View>
                  <View style={styles.progressBar}>
                    <View 
                      style={[
                        styles.progressFill, 
                        { 
                          width: `${progressPercentage}%`,
                          backgroundColor: subject.color 
                        }
                      ]} 
                    />
                  </View>
                </View>

                {/* Topic Preview */}
                <View style={styles.topicsPreview}>
                  <Text style={styles.topicsPreviewTitle}>Key Topics:</Text>
                  <View style={styles.topicsContainer}>
                    {subject.topics.slice(0, 3).map((topic, index) => (
                      <View key={index} style={styles.topicChip}>
                        <View style={styles.topicStatus}>
                          {topic.completed ? (
                            <CheckCircle2 size={12} color="#059669" />
                          ) : (
                            <View style={styles.pendingDot} />
                          )}
                        </View>
                        <Text style={[
                          styles.topicText,
                          topic.completed && styles.completedTopicText
                        ]}>
                          {topic.name}
                        </Text>
                      </View>
                    ))}
                    {subject.topics.length > 3 && (
                      <View style={styles.topicChip}>
                        <Text style={styles.topicText}>+{subject.topics.length - 3} more</Text>
                      </View>
                    )}
                  </View>
                </View>

                <View style={styles.actionButtons}>
                  <TouchableOpacity style={[styles.actionButton, styles.studyButton]}>
                    <Play size={16} color="#ffffff" />
                    <Text style={styles.studyButtonText}>Study</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.actionButton, styles.videosButton]}>
                    <Youtube size={16} color="#dc2626" />
                    <Text style={styles.videosButtonText}>Videos</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.actionButton, styles.notesButton]}>
                    <FileText size={16} color="#7c3aed" />
                    <Text style={styles.notesButtonText}>Notes</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.actionButton, styles.testButton]}>
                    <Target size={16} color="#059669" />
                    <Text style={styles.testButtonText}>Test</Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Quick Stats */}
        <View style={styles.quickStatsContainer}>
          <Text style={styles.sectionTitle}>Syllabus Statistics</Text>
          <View style={styles.quickStatsGrid}>
            <View style={styles.quickStatCard}>
              <BookOpen size={20} color="#1e40af" />
              <Text style={styles.quickStatNumber}>{syllabusStats.totalSubjects}</Text>
              <Text style={styles.quickStatLabel}>Subjects</Text>
            </View>
            <View style={styles.quickStatCard}>
              <Target size={20} color="#059669" />
              <Text style={styles.quickStatNumber}>{syllabusStats.totalTopics}</Text>
              <Text style={styles.quickStatLabel}>Main Topics</Text>
            </View>
            <View style={styles.quickStatCard}>
              <CheckCircle2 size={20} color="#ea580c" />
              <Text style={styles.quickStatNumber}>{syllabusStats.totalSubtopics}</Text>
              <Text style={styles.quickStatLabel}>Subtopics</Text>
            </View>
            <View style={styles.quickStatCard}>
              <Zap size={20} color="#7c3aed" />
              <Text style={styles.quickStatNumber}>{syllabusStats.overallProgress}%</Text>
              <Text style={styles.quickStatLabel}>Complete</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  sectionFilterContainer: {
    paddingLeft: 20,
    marginBottom: 16,
  },
  sectionFilter: {
    paddingRight: 20,
  },
  sectionButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  activeSectionButton: {
    backgroundColor: '#1e40af',
    borderColor: '#1e40af',
  },
  sectionButtonText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activeSectionButtonText: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  overallProgressContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  subjectCount: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  overallProgressCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  progressStat: {
    alignItems: 'center',
  },
  progressNumber: {
    fontSize: 24,
    fontFamily: 'RobotoSlab-Bold',
    color: '#1e40af',
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 4,
  },
  overallProgressBar: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
  },
  overallProgressFill: {
    height: '100%',
    backgroundColor: '#1e40af',
    borderRadius: 4,
  },
  subjectsContainer: {
    marginBottom: 24,
  },
  subjectCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  subjectHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  subjectIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  subjectInfo: {
    flex: 1,
  },
  subjectName: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  subjectSection: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#7c3aed',
    marginBottom: 4,
  },
  subjectDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  subjectMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressPercentage: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  topicsPreview: {
    marginBottom: 16,
  },
  topicsPreviewTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  topicsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  topicChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 6,
  },
  topicStatus: {
    width: 12,
    height: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pendingDot: {
    width: 6,
    height: 6,
    backgroundColor: '#9ca3af',
    borderRadius: 3,
  },
  topicText: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
  },
  completedTopicText: {
    color: '#059669',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 10,
    gap: 6,
  },
  studyButton: {
    backgroundColor: '#1e40af',
  },
  studyButtonText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  videosButton: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  videosButtonText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#dc2626',
  },
  notesButton: {
    backgroundColor: '#faf5ff',
    borderWidth: 1,
    borderColor: '#e9d5ff',
  },
  notesButtonText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#7c3aed',
  },
  testButton: {
    backgroundColor: '#ecfdf5',
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  testButtonText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
  quickStatsContainer: {
    marginBottom: 24,
  },
  quickStatsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  quickStatCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  quickStatNumber: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  quickStatLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
});