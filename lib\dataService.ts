import { supabase } from './supabase';

// Study Session Types
export interface StudySession {
  id: string;
  user_id: string;
  date: string;
  subject: string;
  topic: string;
  duration: number; // in minutes
  type: 'study' | 'test' | 'revision';
  score?: number;
  created_at: string;
  updated_at: string;
}

// Achievement Types
export interface Achievement {
  id: string;
  user_id: string;
  title: string;
  description: string;
  icon: string;
  earned: boolean;
  earned_date?: string;
  category: 'study' | 'test' | 'streak' | 'milestone';
  created_at: string;
}

// Progress Types
export interface UserProgress {
  id: string;
  user_id: string;
  subject: string;
  topic: string;
  completion_percentage: number;
  last_studied: string;
  total_time_spent: number; // in minutes
  best_score?: number;
  created_at: string;
  updated_at: string;
}

// Test Result Types
export interface TestResult {
  id: string;
  user_id: string;
  test_name: string;
  test_type: 'mock' | 'practice' | 'daily_quiz';
  subject: string;
  total_questions: number;
  correct_answers: number;
  score_percentage: number;
  time_taken: number; // in minutes
  completed_at: string;
  created_at: string;
}

// Study Session Functions
export const createStudySession = async (sessionData: Omit<StudySession, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('study_sessions')
    .insert(sessionData)
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const getUserStudySessions = async (userId: string, limit = 10) => {
  const { data, error } = await supabase
    .from('study_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('date', { ascending: false })
    .limit(limit);

  if (error) throw error;
  return data || [];
};

export const getStudyStats = async (userId: string) => {
  // Get total study hours
  const { data: sessions, error: sessionsError } = await supabase
    .from('study_sessions')
    .select('duration')
    .eq('user_id', userId);

  if (sessionsError) throw sessionsError;

  const totalMinutes = sessions?.reduce((sum, session) => sum + session.duration, 0) || 0;
  const totalHours = Math.round(totalMinutes / 60);

  // Get test results for average score
  const { data: testResults, error: testError } = await supabase
    .from('test_results')
    .select('score_percentage')
    .eq('user_id', userId);

  if (testError) throw testError;

  const averageScore = testResults?.length > 0 
    ? Math.round(testResults.reduce((sum, test) => sum + test.score_percentage, 0) / testResults.length)
    : 0;

  // Calculate streak (simplified - would need more complex logic for real streak calculation)
  const currentStreak = 12; // This would be calculated based on consecutive study days

  return {
    totalStudyHours: totalHours,
    totalSessions: sessions?.length || 0,
    averageScore,
    currentStreak,
    totalTests: testResults?.length || 0,
  };
};

// Achievement Functions
export const getUserAchievements = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_achievements')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
};

export const createAchievement = async (achievementData: Omit<Achievement, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('user_achievements')
    .insert(achievementData)
    .select()
    .single();

  if (error) throw error;
  return data;
};

// Progress Functions
export const getUserProgress = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_progress')
    .select('*')
    .eq('user_id', userId)
    .order('last_studied', { ascending: false });

  if (error) throw error;
  return data || [];
};

export const updateProgress = async (userId: string, subject: string, topic: string, progressData: Partial<UserProgress>) => {
  const { data, error } = await supabase
    .from('user_progress')
    .upsert({
      user_id: userId,
      subject,
      topic,
      ...progressData,
      updated_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

// Test Result Functions
export const createTestResult = async (testData: Omit<TestResult, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('test_results')
    .insert(testData)
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const getUserTestResults = async (userId: string, limit = 10) => {
  const { data, error } = await supabase
    .from('test_results')
    .select('*')
    .eq('user_id', userId)
    .order('completed_at', { ascending: false })
    .limit(limit);

  if (error) throw error;
  return data || [];
};

// Weekly Activity (derived from study sessions)
export const getWeeklyActivity = async (userId: string) => {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const { data: sessions, error } = await supabase
    .from('study_sessions')
    .select('*')
    .eq('user_id', userId)
    .gte('date', oneWeekAgo.toISOString())
    .order('date', { ascending: true });

  if (error) throw error;

  // Group sessions by day
  const weeklyData = [];
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  for (let i = 0; i < 7; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (6 - i));
    
    const dayName = days[date.getDay()];
    const dayData = sessions?.filter(session => {
      const sessionDate = new Date(session.date);
      return sessionDate.toDateString() === date.toDateString();
    }) || [];

    const studyHours = dayData.reduce((sum, session) => sum + session.duration, 0) / 60;
    const testsCompleted = dayData.filter(session => session.type === 'test').length;
    const topicsCompleted = new Set(dayData.map(session => session.topic)).size;

    weeklyData.push({
      day: dayName,
      date: date,
      studyHours: Math.round(studyHours * 10) / 10,
      testsCompleted,
      topicsCompleted,
      xpEarned: Math.round(studyHours * 50 + testsCompleted * 100), // Simple XP calculation
    });
  }

  return weeklyData;
};

// Subject Progress
export const getSubjectProgress = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_progress')
    .select('subject, completion_percentage, total_time_spent')
    .eq('user_id', userId);

  if (error) throw error;

  // Group by subject and calculate averages
  const subjectMap = new Map();
  
  data?.forEach(progress => {
    if (!subjectMap.has(progress.subject)) {
      subjectMap.set(progress.subject, {
        subject: progress.subject,
        totalCompletion: 0,
        totalTime: 0,
        topicCount: 0,
      });
    }
    
    const subjectData = subjectMap.get(progress.subject);
    subjectData.totalCompletion += progress.completion_percentage;
    subjectData.totalTime += progress.total_time_spent;
    subjectData.topicCount += 1;
  });

  return Array.from(subjectMap.values()).map(subject => ({
    subject: subject.subject,
    averageCompletion: Math.round(subject.totalCompletion / subject.topicCount),
    totalTimeSpent: subject.totalTime,
    topicCount: subject.topicCount,
  }));
};
