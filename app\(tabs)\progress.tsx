import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TrendingUp, Target, Award, Calendar, BookOpen, Clock, ChartBar as BarChart3, Zap, Trophy, CircleCheck as CheckCircle2, TriangleAlert as <PERSON>ert<PERSON><PERSON>gle } from 'lucide-react-native';
import { getProgressStats, getWeeklyStats, weeklyActivity, achievements } from '@/data/progress';
import { getSyllabusStats } from '@/data/syllabus';

const { width } = Dimensions.get('window');

export default function ProgressScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  
  const periods = [
    { id: 'week', label: 'Week' },
    { id: 'month', label: 'Month' },
    { id: 'year', label: 'Year' },
  ];

  const progressStats = getProgressStats();
  const weeklyStats = getWeeklyStats();
  const syllabusStats = getSyllabusStats();

  const overallStats = [
    { label: 'Study Hours', value: `${progressStats.totalStudyHours}h`, icon: Clock, color: '#1e40af', bgColor: '#eff6ff' },
    { label: 'Study Streak', value: `${progressStats.currentStreak} days`, icon: Target, color: '#059669', bgColor: '#ecfdf5' },
    { label: 'Tests Completed', value: progressStats.totalSessions.toString(), icon: Award, color: '#ea580c', bgColor: '#fff7ed' },
    { label: 'XP Points', value: progressStats.totalXP.toString(), icon: Zap, color: '#7c3aed', bgColor: '#faf5ff' },
  ];

  const subjectProgress = [
    { name: 'Engineering Mathematics', progress: 65, color: '#1e40af', score: 82, topics: 6, completed: 4 },
    { name: 'Thermodynamics', progress: 45, color: '#dc2626', score: 78, topics: 6, completed: 3 },
    { name: 'Fluid Mechanics', progress: 72, color: '#0891b2', score: 85, topics: 6, completed: 4 },
    { name: 'Strength of Materials', progress: 68, color: '#059669', score: 79, topics: 6, completed: 4 },
    { name: 'Theory of Machines', progress: 38, color: '#7c3aed', score: 71, topics: 5, completed: 2 },
    { name: 'Machine Design', progress: 42, color: '#ea580c', score: 74, topics: 5, completed: 2 },
    { name: 'Heat Transfer', progress: 55, color: '#be185d', score: 76, topics: 4, completed: 2 },
    { name: 'Manufacturing', progress: 60, color: '#0d9488', score: 80, topics: 8, completed: 5 },
  ];

  const strengths = [
    { topic: 'Linear Algebra', score: 92, subject: 'Mathematics' },
    { topic: 'IC Engines', score: 88, subject: 'Applications' },
    { topic: 'Casting', score: 85, subject: 'Manufacturing' }
  ];
  
  const weaknesses = [
    { topic: 'Complex Variables', score: 58, subject: 'Mathematics' },
    { topic: 'Vibrations', score: 62, subject: 'Theory of Machines' },
    { topic: 'Heat Exchangers', score: 65, subject: 'Heat Transfer' }
  ];

  const studyGoals = [
    {
      title: 'Monthly Target',
      description: 'Complete 25 topics across all subjects',
      progress: 68,
      current: 17,
      target: 25,
      icon: BookOpen,
      color: '#1e40af'
    },
    {
      title: 'Test Performance',
      description: 'Achieve 80% average in mock tests',
      progress: 74,
      current: 74,
      target: 80,
      icon: Target,
      color: '#059669'
    },
    {
      title: 'XP Challenge',
      description: 'Earn 3,000 XP points this month',
      progress: 82,
      current: 2450,
      target: 3000,
      icon: Zap,
      color: '#f59e0b'
    }
  ];

  const maxHours = Math.max(...weeklyActivity.map(day => day.studyHours));

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Progress Analytics</Text>
        <Text style={styles.subtitle}>Track your GATE preparation journey</Text>
      </View>

      {/* Period Selector */}
      <View style={styles.periodContainer}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              selectedPeriod === period.id && styles.activePeriodButton
            ]}
            onPress={() => setSelectedPeriod(period.id)}
          >
            <Text
              style={[
                styles.periodText,
                selectedPeriod === period.id && styles.activePeriodText
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {/* Overall Stats */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Overall Statistics</Text>
          <View style={styles.statsGrid}>
            {overallStats.map((stat, index) => (
              <View key={index} style={[styles.statCard, { backgroundColor: stat.bgColor }]}>
                <View style={styles.statIcon}>
                  <stat.icon size={20} color={stat.color} />
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Weekly Activity Chart */}
        <View style={styles.chartContainer}>
          <Text style={styles.sectionTitle}>Weekly Activity</Text>
          <View style={styles.chartCard}>
            <View style={styles.chartHeader}>
              <View style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: '#1e40af' }]} />
                <Text style={styles.legendText}>Study Hours</Text>
              </View>
              <View style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: '#059669' }]} />
                <Text style={styles.legendText}>Tests</Text>
              </View>
              <View style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: '#ea580c' }]} />
                <Text style={styles.legendText}>Topics</Text>
              </View>
            </View>
            <View style={styles.chart}>
              {weeklyActivity.map((day, index) => (
                <View key={index} style={styles.chartColumn}>
                  <View style={styles.chartBars}>
                    <View
                      style={[
                        styles.chartBar,
                        styles.hoursBar,
                        { height: (day.studyHours / maxHours) * 80 }
                      ]}
                    />
                    <View
                      style={[
                        styles.chartBar,
                        styles.testsBar,
                        { height: day.testsCompleted * 15 }
                      ]}
                    />
                    <View
                      style={[
                        styles.chartBar,
                        styles.topicsBar,
                        { height: day.topicsCompleted * 12 }
                      ]}
                    />
                  </View>
                  <Text style={styles.chartLabel}>{day.day}</Text>
                </View>
              ))}
            </View>
            <View style={styles.chartSummary}>
              <Text style={styles.chartSummaryText}>
                This week: {weeklyStats.totalHours}h study • {weeklyStats.totalTests} tests • {weeklyStats.totalXP} XP
              </Text>
            </View>
          </View>
        </View>

        {/* Subject Progress */}
        <View style={styles.subjectContainer}>
          <Text style={styles.sectionTitle}>Subject-wise Progress</Text>
          {subjectProgress.map((subject, index) => (
            <View key={index} style={styles.subjectCard}>
              <View style={styles.subjectHeader}>
                <Text style={styles.subjectName}>{subject.name}</Text>
                <View style={styles.subjectStats}>
                  <Text style={styles.subjectScore}>{subject.score}%</Text>
                  <Text style={styles.subjectTopics}>{subject.completed}/{subject.topics}</Text>
                </View>
              </View>
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${subject.progress}%`,
                        backgroundColor: subject.color
                      }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>{subject.progress}%</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Strengths and Weaknesses */}
        <View style={styles.analysisContainer}>
          <Text style={styles.sectionTitle}>Performance Analysis</Text>
          <View style={styles.analysisGrid}>
            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <CheckCircle2 size={20} color="#059669" />
                <Text style={styles.analysisTitle}>Strengths</Text>
              </View>
              {strengths.map((strength, index) => (
                <View key={index} style={styles.analysisItem}>
                  <View style={styles.analysisItemContent}>
                    <Text style={styles.analysisItemTitle}>{strength.topic}</Text>
                    <Text style={styles.analysisItemSubject}>{strength.subject}</Text>
                  </View>
                  <Text style={[styles.analysisScore, { color: '#059669' }]}>{strength.score}%</Text>
                </View>
              ))}
            </View>
            
            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <AlertTriangle size={20} color="#dc2626" />
                <Text style={styles.analysisTitle}>Areas to Improve</Text>
              </View>
              {weaknesses.map((weakness, index) => (
                <View key={index} style={styles.analysisItem}>
                  <View style={styles.analysisItemContent}>
                    <Text style={styles.analysisItemTitle}>{weakness.topic}</Text>
                    <Text style={styles.analysisItemSubject}>{weakness.subject}</Text>
                  </View>
                  <Text style={[styles.analysisScore, { color: '#dc2626' }]}>{weakness.score}%</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Achievements */}
        <View style={styles.achievementsContainer}>
          <Text style={styles.sectionTitle}>Achievements</Text>
          <View style={styles.achievementsGrid}>
            {achievements.map((achievement) => (
              <View 
                key={achievement.id} 
                style={[
                  styles.achievementCard,
                  !achievement.earned && styles.lockedAchievement
                ]}
              >
                <View style={styles.achievementIcon}>
                  <Trophy 
                    size={20} 
                    color={achievement.earned ? '#f59e0b' : '#9ca3af'} 
                    fill={achievement.earned ? '#f59e0b' : 'transparent'}
                  />
                </View>
                <Text 
                  style={[
                    styles.achievementTitle,
                    !achievement.earned && styles.lockedText
                  ]}
                >
                  {achievement.title}
                </Text>
                <Text 
                  style={[
                    styles.achievementDescription,
                    !achievement.earned && styles.lockedText
                  ]}
                >
                  {achievement.description}
                </Text>
                {achievement.earned && achievement.earnedDate && (
                  <Text style={styles.achievementDate}>
                    {achievement.earnedDate.toLocaleDateString()}
                  </Text>
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Study Goals */}
        <View style={styles.goalsContainer}>
          <Text style={styles.sectionTitle}>Current Goals</Text>
          {studyGoals.map((goal, index) => (
            <View key={index} style={styles.goalCard}>
              <View style={styles.goalHeader}>
                <goal.icon size={20} color={goal.color} />
                <Text style={styles.goalTitle}>{goal.title}</Text>
              </View>
              <Text style={styles.goalDescription}>{goal.description}</Text>
              <View style={styles.goalProgress}>
                <View style={styles.goalProgressBar}>
                  <View style={[styles.goalProgressFill, { width: `${goal.progress}%`, backgroundColor: goal.color }]} />
                </View>
                <Text style={styles.goalProgressText}>
                  {typeof goal.current === 'string' ? goal.current : `${goal.current}/${goal.target}`}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Syllabus Overview */}
        <View style={styles.syllabusContainer}>
          <Text style={styles.sectionTitle}>Syllabus Overview</Text>
          <View style={styles.syllabusCard}>
            <View style={styles.syllabusStats}>
              <View style={styles.syllabusStat}>
                <BookOpen size={20} color="#1e40af" />
                <Text style={styles.syllabusStatNumber}>{syllabusStats.totalSubjects}</Text>
                <Text style={styles.syllabusStatLabel}>Subjects</Text>
              </View>
              <View style={styles.syllabusStat}>
                <Target size={20} color="#059669" />
                <Text style={styles.syllabusStatNumber}>{syllabusStats.totalTopics}</Text>
                <Text style={styles.syllabusStatLabel}>Main Topics</Text>
              </View>
              <View style={styles.syllabusStat}>
                <CheckCircle2 size={20} color="#ea580c" />
                <Text style={styles.syllabusStatNumber}>{syllabusStats.totalSubtopics}</Text>
                <Text style={styles.syllabusStatLabel}>Subtopics</Text>
              </View>
            </View>
            <View style={styles.overallProgressBar}>
              <View 
                style={[
                  styles.overallProgressFill, 
                  { width: `${syllabusStats.overallProgress}%` }
                ]} 
              />
            </View>
            <Text style={styles.overallProgressText}>
              {syllabusStats.overallProgress}% Complete • {syllabusStats.completedSubtopics}/{syllabusStats.totalSubtopics} Topics Done
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  periodContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    alignItems: 'center',
  },
  activePeriodButton: {
    backgroundColor: '#1e40af',
  },
  periodText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activePeriodText: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - 60) / 2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statIcon: {
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
  chartContainer: {
    marginBottom: 24,
  },
  chartCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
    marginBottom: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  legendText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  chart: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 120,
    marginBottom: 16,
  },
  chartColumn: {
    alignItems: 'center',
    flex: 1,
  },
  chartBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 100,
    gap: 2,
  },
  chartBar: {
    width: 6,
    borderRadius: 3,
  },
  hoursBar: {
    backgroundColor: '#1e40af',
  },
  testsBar: {
    backgroundColor: '#059669',
  },
  topicsBar: {
    backgroundColor: '#ea580c',
  },
  chartLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    marginTop: 8,
  },
  chartSummary: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  chartSummaryText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  subjectContainer: {
    marginBottom: 24,
  },
  subjectCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  subjectName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  subjectStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  subjectScore: {
    fontSize: 14,
    fontFamily: 'RobotoSlab-Bold',
    color: '#059669',
  },
  subjectTopics: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    minWidth: 35,
    textAlign: 'right',
  },
  analysisContainer: {
    marginBottom: 24,
  },
  analysisGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  analysisCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  analysisTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  analysisItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  analysisItemContent: {
    flex: 1,
  },
  analysisItemTitle: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  analysisItemSubject: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  analysisScore: {
    fontSize: 12,
    fontFamily: 'RobotoSlab-Bold',
  },
  achievementsContainer: {
    marginBottom: 24,
  },
  achievementsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  achievementCard: {
    width: (width - 60) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  lockedAchievement: {
    opacity: 0.6,
  },
  achievementIcon: {
    marginBottom: 8,
  },
  achievementTitle: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
    textAlign: 'center',
  },
  achievementDescription: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 14,
    marginBottom: 4,
  },
  achievementDate: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
    textAlign: 'center',
  },
  lockedText: {
    color: '#9ca3af',
  },
  goalsContainer: {
    marginBottom: 24,
  },
  goalCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  goalTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  goalDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 12,
  },
  goalProgress: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalProgressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginRight: 12,
  },
  goalProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  goalProgressText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  syllabusContainer: {
    marginBottom: 24,
  },
  syllabusCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  syllabusStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  syllabusStat: {
    alignItems: 'center',
  },
  syllabusStatNumber: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  syllabusStatLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
  overallProgressBar: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginBottom: 12,
  },
  overallProgressFill: {
    height: '100%',
    backgroundColor: '#1e40af',
    borderRadius: 4,
  },
  overallProgressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
});