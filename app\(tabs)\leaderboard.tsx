import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Trophy, Medal, Star, TrendingUp, Users, Target, Crown, Award, Zap, Calendar } from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function LeaderboardScreen() {
  const [selectedCategory, setSelectedCategory] = useState('overall');
  const [selectedPeriod, setSelectedPeriod] = useState('weekly');

  const categories = [
    { id: 'overall', name: 'Overall', icon: Trophy },
    { id: 'tests', name: 'Tests', icon: Target },
    { id: 'study-time', name: 'Study Time', icon: TrendingUp },
    { id: 'streak', name: 'Streak', icon: Zap },
  ];

  const periods = [
    { id: 'weekly', name: 'This Week' },
    { id: 'monthly', name: 'This Month' },
    { id: 'all-time', name: 'All Time' },
  ];

  const topPerformers = [
    {
      rank: 1,
      name: '<PERSON><PERSON>',
      avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2850,
      change: '+12',
      badge: 'gold',
      location: 'Delhi',
      college: 'IIT Delhi',
      streak: 28,
      testsCompleted: 45
    },
    {
      rank: 2,
      name: 'Priya Singh',
      avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2720,
      change: '+8',
      badge: 'silver',
      location: 'Mumbai',
      college: 'IIT Bombay',
      streak: 25,
      testsCompleted: 42
    },
    {
      rank: 3,
      name: 'Amit Kumar',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2680,
      change: '+15',
      badge: 'bronze',
      location: 'Bangalore',
      college: 'IIT Kanpur',
      streak: 22,
      testsCompleted: 38
    }
  ];

  const leaderboardData = [
    {
      rank: 4,
      name: 'Sneha Patel',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2580,
      change: '+5',
      location: 'Ahmedabad',
      college: 'NIT Surat'
    },
    {
      rank: 5,
      name: 'Vikash Gupta',
      avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2520,
      change: '-2',
      location: 'Pune',
      college: 'COEP'
    },
    {
      rank: 6,
      name: 'Ananya Roy',
      avatar: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2480,
      change: '+7',
      location: 'Kolkata',
      college: 'Jadavpur University'
    },
    {
      rank: 7,
      name: 'Rohit Mehta',
      avatar: 'https://images.pexels.com/photos/1212984/pexels-photo-1212984.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2450,
      change: '+3',
      location: 'Chennai',
      college: 'IIT Madras'
    },
    {
      rank: 8,
      name: 'Kavya Nair',
      avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2420,
      change: '+1',
      location: 'Kochi',
      college: 'NIT Calicut'
    },
    {
      rank: 9,
      name: 'Arjun Reddy',
      avatar: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
      score: 2380,
      change: '-4',
      location: 'Hyderabad',
      college: 'BITS Pilani'
    },
    {
      rank: 10,
      name: 'You',
      avatar: null,
      score: 2350,
      change: '+6',
      location: 'Your City',
      college: 'Your College',
      isCurrentUser: true
    }
  ];

  const achievements = [
    {
      title: 'Top Scorer',
      description: 'Highest score this week',
      winner: 'Rahul Sharma',
      score: '98%',
      icon: Trophy,
      color: '#f59e0b'
    },
    {
      title: 'Study Champion',
      description: 'Most study hours',
      winner: 'Priya Singh',
      score: '42h',
      icon: TrendingUp,
      color: '#1e40af'
    },
    {
      title: 'Streak Master',
      description: 'Longest study streak',
      winner: 'Amit Kumar',
      score: '28 days',
      icon: Zap,
      color: '#dc2626'
    }
  ];

  const myStats = {
    rank: 10,
    score: 2350,
    percentile: 85,
    change: '+6',
    weeklyRank: 8,
    monthlyRank: 12
  };

  const getBadgeIcon = (badge: string) => {
    switch (badge) {
      case 'gold': return <Crown size={16} color="#f59e0b" />;
      case 'silver': return <Medal size={16} color="#6b7280" />;
      case 'bronze': return <Award size={16} color="#ea580c" />;
      default: return null;
    }
  };

  const getChangeColor = (change: string) => {
    if (change.startsWith('+')) return '#059669';
    if (change.startsWith('-')) return '#dc2626';
    return '#6b7280';
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Leaderboard</Text>
        <Text style={styles.subtitle}>Compete with fellow GATE aspirants</Text>
      </View>

      {/* Category Tabs */}
      <View style={styles.categoryContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryTab,
                selectedCategory === category.id && styles.activeCategoryTab
              ]}
              onPress={() => setSelectedCategory(category.id)}
            >
              <category.icon 
                size={16} 
                color={selectedCategory === category.id ? '#ffffff' : '#6b7280'} 
              />
              <Text
                style={[
                  styles.categoryTabText,
                  selectedCategory === category.id && styles.activeCategoryTabText
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Period Selector */}
      <View style={styles.periodContainer}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              selectedPeriod === period.id && styles.activePeriodButton
            ]}
            onPress={() => setSelectedPeriod(period.id)}
          >
            <Text
              style={[
                styles.periodText,
                selectedPeriod === period.id && styles.activePeriodText
              ]}
            >
              {period.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {/* My Rank Card */}
        <View style={styles.myRankContainer}>
          <Text style={styles.sectionTitle}>Your Performance</Text>
          <View style={styles.myRankCard}>
            <View style={styles.myRankHeader}>
              <View style={styles.myRankInfo}>
                <Text style={styles.myRankNumber}>#{myStats.rank}</Text>
                <Text style={styles.myRankLabel}>Current Rank</Text>
              </View>
              <View style={styles.myRankStats}>
                <View style={styles.myRankStat}>
                  <Text style={styles.myRankStatNumber}>{myStats.score}</Text>
                  <Text style={styles.myRankStatLabel}>Score</Text>
                </View>
                <View style={styles.myRankStat}>
                  <Text style={styles.myRankStatNumber}>{myStats.percentile}%</Text>
                  <Text style={styles.myRankStatLabel}>Percentile</Text>
                </View>
              </View>
            </View>
            <View style={styles.myRankFooter}>
              <View style={styles.rankChange}>
                <TrendingUp size={16} color={getChangeColor(myStats.change)} />
                <Text style={[styles.rankChangeText, { color: getChangeColor(myStats.change) }]}>
                  {myStats.change} this week
                </Text>
              </View>
              <View style={styles.periodRanks}>
                <Text style={styles.periodRankText}>Weekly: #{myStats.weeklyRank}</Text>
                <Text style={styles.periodRankText}>Monthly: #{myStats.monthlyRank}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Top 3 Podium */}
        <View style={styles.podiumContainer}>
          <Text style={styles.sectionTitle}>Top Performers</Text>
          <View style={styles.podium}>
            {/* Second Place */}
            <View style={[styles.podiumPosition, styles.secondPlace]}>
              <Image source={{ uri: topPerformers[1].avatar }} style={styles.podiumAvatar} />
              <View style={styles.podiumBadge}>
                {getBadgeIcon(topPerformers[1].badge)}
              </View>
              <Text style={styles.podiumName}>{topPerformers[1].name}</Text>
              <Text style={styles.podiumScore}>{topPerformers[1].score}</Text>
              <Text style={styles.podiumRank}>2</Text>
            </View>

            {/* First Place */}
            <View style={[styles.podiumPosition, styles.firstPlace]}>
              <Image source={{ uri: topPerformers[0].avatar }} style={styles.podiumAvatar} />
              <View style={styles.podiumBadge}>
                {getBadgeIcon(topPerformers[0].badge)}
              </View>
              <Text style={styles.podiumName}>{topPerformers[0].name}</Text>
              <Text style={styles.podiumScore}>{topPerformers[0].score}</Text>
              <Text style={styles.podiumRank}>1</Text>
            </View>

            {/* Third Place */}
            <View style={[styles.podiumPosition, styles.thirdPlace]}>
              <Image source={{ uri: topPerformers[2].avatar }} style={styles.podiumAvatar} />
              <View style={styles.podiumBadge}>
                {getBadgeIcon(topPerformers[2].badge)}
              </View>
              <Text style={styles.podiumName}>{topPerformers[2].name}</Text>
              <Text style={styles.podiumScore}>{topPerformers[2].score}</Text>
              <Text style={styles.podiumRank}>3</Text>
            </View>
          </View>
        </View>

        {/* Weekly Achievements */}
        <View style={styles.achievementsContainer}>
          <Text style={styles.sectionTitle}>Weekly Champions</Text>
          <View style={styles.achievementsGrid}>
            {achievements.map((achievement, index) => (
              <View key={index} style={styles.achievementCard}>
                <View style={[styles.achievementIcon, { backgroundColor: achievement.color + '15' }]}>
                  <achievement.icon size={20} color={achievement.color} />
                </View>
                <Text style={styles.achievementTitle}>{achievement.title}</Text>
                <Text style={styles.achievementDescription}>{achievement.description}</Text>
                <Text style={styles.achievementWinner}>{achievement.winner}</Text>
                <Text style={styles.achievementScore}>{achievement.score}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Full Leaderboard */}
        <View style={styles.leaderboardContainer}>
          <Text style={styles.sectionTitle}>Full Rankings</Text>
          <View style={styles.leaderboardCard}>
            {leaderboardData.map((user) => (
              <View 
                key={user.rank} 
                style={[
                  styles.leaderboardItem,
                  user.isCurrentUser && styles.currentUserItem
                ]}
              >
                <View style={styles.userRank}>
                  <Text style={[
                    styles.rankText,
                    user.rank <= 3 && styles.topRankText,
                    user.isCurrentUser && styles.currentUserRank
                  ]}>
                    {user.rank}
                  </Text>
                </View>
                
                <View style={styles.userInfo}>
                  {user.avatar ? (
                    <Image source={{ uri: user.avatar }} style={styles.userAvatar} />
                  ) : (
                    <View style={styles.userAvatarPlaceholder}>
                      <Text style={styles.userAvatarText}>Y</Text>
                    </View>
                  )}
                  <View style={styles.userDetails}>
                    <Text style={[
                      styles.userName,
                      user.isCurrentUser && styles.currentUserName
                    ]}>
                      {user.name}
                    </Text>
                    <Text style={styles.userLocation}>{user.location}</Text>
                    <Text style={styles.userCollege}>{user.college}</Text>
                  </View>
                </View>

                <View style={styles.userStats}>
                  <Text style={styles.userScore}>{user.score}</Text>
                  <View style={styles.userChange}>
                    <Text style={[styles.changeText, { color: getChangeColor(user.change) }]}>
                      {user.change}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Competition Stats */}
        <View style={styles.competitionContainer}>
          <Text style={styles.sectionTitle}>Competition Stats</Text>
          <View style={styles.competitionGrid}>
            <View style={styles.competitionCard}>
              <Users size={24} color="#1e40af" />
              <Text style={styles.competitionNumber}>15,247</Text>
              <Text style={styles.competitionLabel}>Total Participants</Text>
            </View>
            <View style={styles.competitionCard}>
              <Calendar size={24} color="#059669" />
              <Text style={styles.competitionNumber}>1,856</Text>
              <Text style={styles.competitionLabel}>Active This Week</Text>
            </View>
            <View style={styles.competitionCard}>
              <Target size={24} color="#ea580c" />
              <Text style={styles.competitionNumber}>89%</Text>
              <Text style={styles.competitionLabel}>Avg Score</Text>
            </View>
            <View style={styles.competitionCard}>
              <Trophy size={24} color="#f59e0b" />
              <Text style={styles.competitionNumber}>2,850</Text>
              <Text style={styles.competitionLabel}>Top Score</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  categoryContainer: {
    paddingLeft: 20,
    paddingVertical: 16,
  },
  categoryScroll: {
    paddingRight: 20,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    gap: 6,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  activeCategoryTab: {
    backgroundColor: '#1e40af',
    borderColor: '#1e40af',
  },
  categoryTabText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activeCategoryTabText: {
    color: '#ffffff',
  },
  periodContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 16,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    alignItems: 'center',
  },
  activePeriodButton: {
    backgroundColor: '#1e40af',
  },
  periodText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activePeriodText: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  myRankContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  myRankCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  myRankHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  myRankInfo: {
    alignItems: 'center',
  },
  myRankNumber: {
    fontSize: 32,
    fontFamily: 'RobotoSlab-Bold',
    color: '#1e40af',
  },
  myRankLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  myRankStats: {
    flexDirection: 'row',
    gap: 24,
  },
  myRankStat: {
    alignItems: 'center',
  },
  myRankStatNumber: {
    fontSize: 20,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
  },
  myRankStatLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  myRankFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  rankChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  rankChangeText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  periodRanks: {
    alignItems: 'flex-end',
  },
  periodRankText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  podiumContainer: {
    marginBottom: 24,
  },
  podium: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    height: 200,
    gap: 8,
  },
  podiumPosition: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
  },
  firstPlace: {
    height: 180,
    width: 100,
    borderTopWidth: 3,
    borderTopColor: '#f59e0b',
  },
  secondPlace: {
    height: 160,
    width: 90,
    borderTopWidth: 3,
    borderTopColor: '#6b7280',
  },
  thirdPlace: {
    height: 140,
    width: 90,
    borderTopWidth: 3,
    borderTopColor: '#ea580c',
  },
  podiumAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: 8,
  },
  podiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  podiumName: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 4,
  },
  podiumScore: {
    fontSize: 14,
    fontFamily: 'RobotoSlab-Bold',
    color: '#1e40af',
    marginBottom: 4,
  },
  podiumRank: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#f59e0b',
  },
  achievementsContainer: {
    marginBottom: 24,
  },
  achievementsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  achievementCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  achievementTitle: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 6,
  },
  achievementWinner: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 2,
  },
  achievementScore: {
    fontSize: 12,
    fontFamily: 'RobotoSlab-Bold',
    color: '#1e40af',
    textAlign: 'center',
  },
  leaderboardContainer: {
    marginBottom: 24,
  },
  leaderboardCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  currentUserItem: {
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    paddingHorizontal: 8,
    marginHorizontal: -8,
  },
  userRank: {
    width: 40,
    alignItems: 'center',
  },
  rankText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#6b7280',
  },
  topRankText: {
    color: '#f59e0b',
  },
  currentUserRank: {
    color: '#1e40af',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1e40af',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userAvatarText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  currentUserName: {
    color: '#1e40af',
  },
  userLocation: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  userCollege: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
  },
  userStats: {
    alignItems: 'flex-end',
  },
  userScore: {
    fontSize: 16,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
  },
  userChange: {
    marginTop: 2,
  },
  changeText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  competitionContainer: {
    marginBottom: 24,
  },
  competitionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  competitionCard: {
    width: (width - 64) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  competitionNumber: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  competitionLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    textAlign: 'center',
  },
});