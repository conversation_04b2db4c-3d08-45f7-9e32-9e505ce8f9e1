import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Image, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Search, Play, Clock, Eye, Star, Filter, BookOpen, Download, Share, Heart, ChevronRight } from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function VideosScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedFilter, setSelectedFilter] = useState('latest');

  const categories = [
    { id: 'all', name: 'All', count: 245 },
    { id: 'mathematics', name: 'Mathematics', count: 45 },
    { id: 'thermodynamics', name: 'Thermodynamics', count: 38 },
    { id: 'fluid-mechanics', name: 'Fluid Mechanics', count: 32 },
    { id: 'strength', name: 'Strength of Materials', count: 28 },
    { id: 'manufacturing', name: 'Manufacturing', count: 35 },
    { id: 'machine-design', name: 'Machine Design', count: 25 },
    { id: 'heat-transfer', name: 'Heat Transfer', count: 22 },
    { id: 'theory-machines', name: 'Theory of Machines', count: 20 },
  ];

  const filters = [
    { id: 'latest', name: 'Latest' },
    { id: 'popular', name: 'Most Popular' },
    { id: 'duration', name: 'Duration' },
    { id: 'rating', name: 'Highest Rated' },
  ];

  const featuredVideos = [
    {
      id: 'f1',
      title: 'Complete Linear Algebra for GATE 2025',
      instructor: 'Dr. Rajesh Kumar',
      duration: '2:45:30',
      views: '125K',
      rating: 4.8,
      thumbnail: 'https://images.pexels.com/photos/8199562/pexels-photo-8199562.jpeg?auto=compress&cs=tinysrgb&w=400&h=225&fit=crop',
      category: 'Engineering Mathematics',
      level: 'Intermediate',
      progress: 65,
      isBookmarked: true
    },
    {
      id: 'f2',
      title: 'Thermodynamics First Law Explained',
      instructor: 'Prof. Anita Sharma',
      duration: '1:32:15',
      views: '89K',
      rating: 4.9,
      thumbnail: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=400&h=225&fit=crop',
      category: 'Thermodynamics',
      level: 'Beginner',
      progress: 0,
      isBookmarked: false
    }
  ];

  const videoSeries = [
    {
      id: 's1',
      title: 'GATE Mathematics Complete Series',
      instructor: 'IIT Delhi Faculty',
      videos: 24,
      totalDuration: '18:30:00',
      thumbnail: 'https://images.pexels.com/photos/256541/pexels-photo-256541.jpeg?auto=compress&cs=tinysrgb&w=400&h=225&fit=crop',
      rating: 4.7,
      enrolled: '15.2K',
      price: 'Free'
    },
    {
      id: 's2',
      title: 'Fluid Mechanics Masterclass',
      instructor: 'GATE Academy',
      videos: 18,
      totalDuration: '14:45:00',
      thumbnail: 'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg?auto=compress&cs=tinysrgb&w=400&h=225&fit=crop',
      rating: 4.6,
      enrolled: '12.8K',
      price: '₹999'
    }
  ];

  const recentVideos = [
    {
      id: 'r1',
      title: 'Stress-Strain Diagrams Simplified',
      instructor: 'Mechanical Hub',
      duration: '28:45',
      views: '45K',
      rating: 4.5,
      thumbnail: 'https://images.pexels.com/photos/3862132/pexels-photo-3862132.jpeg?auto=compress&cs=tinysrgb&w=300&h=169&fit=crop',
      uploadedAt: '2 days ago'
    },
    {
      id: 'r2',
      title: 'Heat Exchanger Design Principles',
      instructor: 'Thermal Sciences Pro',
      duration: '42:20',
      views: '32K',
      rating: 4.7,
      thumbnail: 'https://images.pexels.com/photos/2280571/pexels-photo-2280571.jpeg?auto=compress&cs=tinysrgb&w=300&h=169&fit=crop',
      uploadedAt: '1 week ago'
    },
    {
      id: 'r3',
      title: 'Gear Design and Analysis',
      instructor: 'Machine Design Expert',
      duration: '35:15',
      views: '28K',
      rating: 4.6,
      thumbnail: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=300&h=169&fit=crop',
      uploadedAt: '3 days ago'
    }
  ];

  const trendingTopics = [
    { name: 'Linear Algebra', videos: 45, trending: '+12%' },
    { name: 'Heat Transfer', videos: 38, trending: '+8%' },
    { name: 'Fluid Dynamics', videos: 32, trending: '+15%' },
    { name: 'Machine Design', videos: 28, trending: '+6%' }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Video Library</Text>
        <Text style={styles.subtitle}>Learn from expert instructors</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Search size={20} color="#6b7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search videos, topics, instructors..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9ca3af"
          />
        </View>
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color="#1e40af" />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.id}
              style={[
                styles.filterTab,
                selectedFilter === filter.id && styles.activeFilterTab
              ]}
              onPress={() => setSelectedFilter(filter.id)}
            >
              <Text
                style={[
                  styles.filterTabText,
                  selectedFilter === filter.id && styles.activeFilterTabText
                ]}
              >
                {filter.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {/* Featured Videos */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Videos</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
            {featuredVideos.map((video) => (
              <TouchableOpacity key={video.id} style={styles.featuredVideoCard}>
                <View style={styles.videoThumbnail}>
                  <Image source={{ uri: video.thumbnail }} style={styles.thumbnailImage} />
                  <View style={styles.playOverlay}>
                    <Play size={24} color="#ffffff" fill="#ffffff" />
                  </View>
                  <View style={styles.durationBadge}>
                    <Text style={styles.durationText}>{video.duration}</Text>
                  </View>
                  <TouchableOpacity style={styles.bookmarkButton}>
                    <Heart 
                      size={16} 
                      color={video.isBookmarked ? "#dc2626" : "#ffffff"} 
                      fill={video.isBookmarked ? "#dc2626" : "transparent"}
                    />
                  </TouchableOpacity>
                  {video.progress > 0 && (
                    <View style={styles.progressOverlay}>
                      <View style={[styles.videoProgress, { width: `${video.progress}%` }]} />
                    </View>
                  )}
                </View>
                <View style={styles.videoInfo}>
                  <Text style={styles.videoTitle} numberOfLines={2}>{video.title}</Text>
                  <Text style={styles.instructorName}>{video.instructor}</Text>
                  <View style={styles.videoMeta}>
                    <View style={styles.metaItem}>
                      <Eye size={12} color="#6b7280" />
                      <Text style={styles.metaText}>{video.views}</Text>
                    </View>
                    <View style={styles.metaItem}>
                      <Star size={12} color="#f59e0b" fill="#f59e0b" />
                      <Text style={styles.metaText}>{video.rating}</Text>
                    </View>
                  </View>
                  <View style={styles.categoryBadge}>
                    <Text style={styles.categoryText}>{video.category}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Video Series */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Complete Series</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          {videoSeries.map((series) => (
            <TouchableOpacity key={series.id} style={styles.seriesCard}>
              <Image source={{ uri: series.thumbnail }} style={styles.seriesThumbnail} />
              <View style={styles.seriesInfo}>
                <Text style={styles.seriesTitle}>{series.title}</Text>
                <Text style={styles.seriesInstructor}>{series.instructor}</Text>
                <View style={styles.seriesMeta}>
                  <Text style={styles.seriesVideos}>{series.videos} videos</Text>
                  <Text style={styles.seriesDuration}>{series.totalDuration}</Text>
                </View>
                <View style={styles.seriesStats}>
                  <View style={styles.seriesRating}>
                    <Star size={12} color="#f59e0b" fill="#f59e0b" />
                    <Text style={styles.ratingText}>{series.rating}</Text>
                  </View>
                  <Text style={styles.enrolledText}>{series.enrolled} enrolled</Text>
                  <View style={styles.priceBadge}>
                    <Text style={styles.priceText}>{series.price}</Text>
                  </View>
                </View>
              </View>
              <ChevronRight size={20} color="#9ca3af" />
            </TouchableOpacity>
          ))}
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Browse by Subject</Text>
          <View style={styles.categoriesGrid}>
            {categories.slice(1, 7).map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryCard,
                  selectedCategory === category.id && styles.activeCategoryCard
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <BookOpen size={24} color="#1e40af" />
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryCount}>{category.count} videos</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Videos */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recently Added</Text>
          {recentVideos.map((video) => (
            <TouchableOpacity key={video.id} style={styles.recentVideoCard}>
              <Image source={{ uri: video.thumbnail }} style={styles.recentThumbnail} />
              <View style={styles.recentVideoInfo}>
                <Text style={styles.recentVideoTitle}>{video.title}</Text>
                <Text style={styles.recentInstructor}>{video.instructor}</Text>
                <View style={styles.recentMeta}>
                  <View style={styles.metaItem}>
                    <Clock size={12} color="#6b7280" />
                    <Text style={styles.metaText}>{video.duration}</Text>
                  </View>
                  <View style={styles.metaItem}>
                    <Eye size={12} color="#6b7280" />
                    <Text style={styles.metaText}>{video.views}</Text>
                  </View>
                  <View style={styles.metaItem}>
                    <Star size={12} color="#f59e0b" fill="#f59e0b" />
                    <Text style={styles.metaText}>{video.rating}</Text>
                  </View>
                </View>
                <Text style={styles.uploadTime}>{video.uploadedAt}</Text>
              </View>
              <View style={styles.videoActions}>
                <TouchableOpacity style={styles.actionButton}>
                  <Download size={16} color="#6b7280" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <Share size={16} color="#6b7280" />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Trending Topics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trending Topics</Text>
          <View style={styles.trendingCard}>
            {trendingTopics.map((topic, index) => (
              <TouchableOpacity key={index} style={styles.trendingItem}>
                <View style={styles.trendingInfo}>
                  <Text style={styles.trendingName}>{topic.name}</Text>
                  <Text style={styles.trendingVideos}>{topic.videos} videos</Text>
                </View>
                <View style={styles.trendingBadge}>
                  <Text style={styles.trendingText}>{topic.trending}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 28,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  filterButton: {
    width: 48,
    height: 48,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  filterContainer: {
    paddingLeft: 20,
    marginBottom: 16,
  },
  filterScroll: {
    paddingRight: 20,
  },
  filterTab: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  activeFilterTab: {
    backgroundColor: '#1e40af',
    borderColor: '#1e40af',
  },
  filterTabText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  activeFilterTabText: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'RobotoSlab-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1e40af',
  },
  horizontalScroll: {
    paddingRight: 20,
  },
  featuredVideoCard: {
    width: 280,
    marginRight: 16,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  videoThumbnail: {
    position: 'relative',
    height: 160,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  playOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
    width: 48,
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  durationText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  bookmarkButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  videoProgress: {
    height: '100%',
    backgroundColor: '#1e40af',
  },
  videoInfo: {
    padding: 16,
  },
  videoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 6,
    lineHeight: 20,
  },
  instructorName: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  videoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  categoryBadge: {
    backgroundColor: '#eff6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  categoryText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#1e40af',
  },
  seriesCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  seriesThumbnail: {
    width: 80,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  seriesInfo: {
    flex: 1,
  },
  seriesTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  seriesInstructor: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 6,
  },
  seriesMeta: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 6,
  },
  seriesVideos: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  seriesDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  seriesStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  seriesRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  enrolledText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  priceBadge: {
    backgroundColor: '#ecfdf5',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priceText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    width: (width - 64) / 2,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  activeCategoryCard: {
    backgroundColor: '#eff6ff',
    borderWidth: 2,
    borderColor: '#1e40af',
  },
  categoryName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  categoryCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  recentVideoCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recentThumbnail: {
    width: 100,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  recentVideoInfo: {
    flex: 1,
  },
  recentVideoTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  recentInstructor: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 6,
  },
  recentMeta: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 4,
  },
  uploadTime: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
  },
  videoActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    backgroundColor: '#f9fafb',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  trendingCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  trendingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  trendingInfo: {
    flex: 1,
  },
  trendingName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  trendingVideos: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginTop: 2,
  },
  trendingBadge: {
    backgroundColor: '#ecfdf5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trendingText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
});