{"name": "gate-mechanical-prep", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@expo-google-fonts/inter": "^0.2.3", "@expo-google-fonts/roboto-slab": "^0.2.3", "expo": "^52.0.30", "expo-blur": "~14.1.3", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~4.0.17", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}