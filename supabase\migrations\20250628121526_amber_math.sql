/*
  # Seed Data for GATE Mechanical Engineering App
  
  This file contains initial data to populate the database with:
  1. GATE 2025 syllabus structure (subjects, topics, subtopics)
  2. Test categories and sample tests
  3. Achievement types and achievements
  4. Notification types
  5. Content categories
  6. Sample learning content
  
  This data provides a complete foundation for the app to function
  with real GATE syllabus content and proper categorization.
*/

-- Insert Test Categories
INSERT INTO test_categories (name, description, color, icon) VALUES
('Mock Tests', 'Full-length GATE simulation tests', '#1e40af', 'file-text'),
('Subject Tests', 'Subject-specific comprehensive tests', '#7c3aed', 'book-open'),
('Topic Tests', 'Focused tests on specific topics', '#059669', 'target'),
('Previous Years', 'GATE previous year question papers', '#dc2626', 'calendar'),
('Daily Challenges', 'Quick daily practice challenges', '#f59e0b', 'zap');

-- Insert Subjects (GATE Mechanical Engineering Syllabus)
INSERT INTO subjects (name, code, section, description, color, bg_color, estimated_hours, difficulty, weightage_percentage, display_order) VALUES
('Engineering Mathematics', 'MATH', 'Section 1: Engineering Mathematics', 'Foundation mathematics for engineering applications', '#1e40af', '#eff6ff', 80, 'Medium', 15, 1),
('Engineering Mechanics', 'MECH', 'Section 2: Applied Mechanics and Design', 'Statics, dynamics, and mechanics of rigid bodies', '#059669', '#ecfdf5', 50, 'Medium', 12, 2),
('Strength of Materials', 'SOM', 'Section 2: Applied Mechanics and Design', 'Stress, strain, and deformation of materials', '#7c3aed', '#faf5ff', 60, 'Hard', 15, 3),
('Theory of Machines', 'TOM', 'Section 2: Applied Mechanics and Design', 'Kinematics, dynamics, and design of machines', '#ea580c', '#fff7ed', 55, 'Hard', 12, 4),
('Machine Design', 'MD', 'Section 2: Applied Mechanics and Design', 'Design of machine elements and systems', '#be185d', '#fdf2f8', 65, 'Hard', 15, 5),
('Fluid Mechanics', 'FM', 'Section 3: Fluid Mechanics and Thermal Sciences', 'Behavior of fluids at rest and in motion', '#0891b2', '#ecfeff', 55, 'Hard', 12, 6),
('Thermodynamics', 'THERMO', 'Section 3: Fluid Mechanics and Thermal Sciences', 'Energy, heat, and work relationships', '#dc2626', '#fef2f2', 65, 'Hard', 15, 7),
('Heat Transfer', 'HT', 'Section 3: Fluid Mechanics and Thermal Sciences', 'Transfer of thermal energy', '#be185d', '#fdf2f8', 50, 'Medium', 10, 8),
('Applications', 'APP', 'Section 3: Fluid Mechanics and Thermal Sciences', 'Power engineering, IC engines, refrigeration, turbomachinery', '#f59e0b', '#fefbf2', 70, 'Hard', 18, 9),
('Engineering Materials', 'MAT', 'Section 4: Materials, Manufacturing and Industrial Engineering', 'Structure and properties of engineering materials', '#0d9488', '#f0fdfa', 40, 'Medium', 8, 10),
('Manufacturing Processes', 'MFG', 'Section 4: Materials, Manufacturing and Industrial Engineering', 'Methods of producing mechanical components', '#ea580c', '#fff7ed', 70, 'Medium', 15, 11),
('Metrology and Inspection', 'MI', 'Section 4: Materials, Manufacturing and Industrial Engineering', 'Measurement and quality control', '#6366f1', '#eef2ff', 30, 'Easy', 5, 12),
('Computer Integrated Manufacturing', 'CIM', 'Section 4: Materials, Manufacturing and Industrial Engineering', 'CAD/CAM and modern manufacturing', '#8b5cf6', '#f5f3ff', 35, 'Medium', 8, 13),
('Production Planning', 'PP', 'Section 4: Materials, Manufacturing and Industrial Engineering', 'Planning and control of production systems', '#059669', '#ecfdf5', 40, 'Medium', 10, 14),
('Operations Research', 'OR', 'Section 4: Materials, Manufacturing and Industrial Engineering', 'Mathematical optimization and decision making', '#1e40af', '#eff6ff', 45, 'Hard', 12, 15);

-- Insert Achievement Types
INSERT INTO achievement_types (name, description, icon, color, display_order) VALUES
('Study Milestones', 'Achievements for study progress and consistency', 'book-open', '#1e40af', 1),
('Test Performance', 'Achievements for test scores and improvements', 'award', '#059669', 2),
('Streak Achievements', 'Achievements for maintaining study streaks', 'zap', '#f59e0b', 3),
('Subject Mastery', 'Achievements for completing subjects', 'graduation-cap', '#7c3aed', 4),
('Special Achievements', 'Rare and special accomplishments', 'star', '#dc2626', 5);

-- Insert Sample Achievements
INSERT INTO achievements (type_id, name, description, criteria, xp_reward, rarity, display_order) VALUES
((SELECT id FROM achievement_types WHERE name = 'Study Milestones'), 'First Steps', 'Complete your first study session', '{"study_sessions": 1}', 50, 'common', 1),
((SELECT id FROM achievement_types WHERE name = 'Study Milestones'), 'Dedicated Learner', 'Complete 10 study sessions', '{"study_sessions": 10}', 200, 'common', 2),
((SELECT id FROM achievement_types WHERE name = 'Study Milestones'), 'Study Marathon', 'Study for 100 hours total', '{"total_study_hours": 100}', 500, 'rare', 3),
((SELECT id FROM achievement_types WHERE name = 'Test Performance'), 'First Test', 'Complete your first mock test', '{"tests_completed": 1}', 100, 'common', 1),
((SELECT id FROM achievement_types WHERE name = 'Test Performance'), 'High Scorer', 'Score above 80% in any test', '{"test_score_above": 80}', 300, 'rare', 2),
((SELECT id FROM achievement_types WHERE name = 'Test Performance'), 'Perfect Score', 'Score 100% in any test', '{"test_score": 100}', 1000, 'legendary', 3),
((SELECT id FROM achievement_types WHERE name = 'Streak Achievements'), 'Week Warrior', 'Maintain a 7-day study streak', '{"study_streak_days": 7}', 250, 'common', 1),
((SELECT id FROM achievement_types WHERE name = 'Streak Achievements'), 'Month Master', 'Maintain a 30-day study streak', '{"study_streak_days": 30}', 750, 'epic', 2),
((SELECT id FROM achievement_types WHERE name = 'Subject Mastery'), 'Math Master', 'Complete Engineering Mathematics', '{"subject_completed": "Engineering Mathematics"}', 400, 'rare', 1),
((SELECT id FROM achievement_types WHERE name = 'Subject Mastery'), 'Thermal Expert', 'Complete Thermodynamics', '{"subject_completed": "Thermodynamics"}', 400, 'rare', 2);

-- Insert Notification Types
INSERT INTO notification_types (name, description, category, icon, color, default_enabled) VALUES
('Study Reminder', 'Daily study session reminders', 'reminder', 'clock', '#1e40af', true),
('Test Available', 'New test available notifications', 'test', 'file-text', '#059669', true),
('Achievement Earned', 'Achievement unlock notifications', 'achievement', 'award', '#f59e0b', true),
('Streak Alert', 'Study streak maintenance alerts', 'reminder', 'zap', '#dc2626', true),
('Goal Progress', 'Daily goal progress updates', 'study', 'target', '#7c3aed', true),
('System Updates', 'App updates and maintenance', 'system', 'info', '#6b7280', true),
('Exam Alerts', 'GATE exam related notifications', 'system', 'calendar', '#dc2626', true),
('AI Recommendations', 'AI tutor recommendations', 'study', 'bot', '#8b5cf6', true);

-- Insert Content Categories
INSERT INTO content_categories (name, description, icon, color, display_order) VALUES
('Video Lectures', 'Educational video content', 'play-circle', '#dc2626', 1),
('Study Materials', 'PDFs and documents', 'file-text', '#1e40af', 2),
('Practice Problems', 'Solved examples and practice', 'calculator', '#059669', 3),
('Formula Sheets', 'Important formulas and references', 'bookmark', '#f59e0b', 4),
('Previous Papers', 'GATE previous year papers', 'archive', '#7c3aed', 5);

-- Insert Sample Learning Content
INSERT INTO learning_content (title, description, content_type, category_id, subject_id, content_url, thumbnail_url, duration_seconds, difficulty, author_name, tags, is_featured) VALUES
('Linear Algebra Fundamentals', 'Complete introduction to linear algebra concepts', 'video', 
 (SELECT id FROM content_categories WHERE name = 'Video Lectures'),
 (SELECT id FROM subjects WHERE code = 'MATH'),
 'https://example.com/video/linear-algebra', 
 'https://images.pexels.com/photos/8199562/pexels-photo-8199562.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop',
 2700, 'Medium', 'Dr. Mathematics', '{"linear algebra", "matrices", "eigenvalues"}', true),

('Thermodynamics First Law', 'Detailed explanation of the first law of thermodynamics', 'video',
 (SELECT id FROM content_categories WHERE name = 'Video Lectures'),
 (SELECT id FROM subjects WHERE code = 'THERMO'),
 'https://example.com/video/first-law',
 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop',
 2280, 'Hard', 'Prof. Thermal', '{"thermodynamics", "first law", "energy"}', true),

('Strength of Materials Basics', 'Introduction to stress and strain concepts', 'video',
 (SELECT id FROM content_categories WHERE name = 'Video Lectures'),
 (SELECT id FROM subjects WHERE code = 'SOM'),
 'https://example.com/video/som-basics',
 'https://images.pexels.com/photos/256541/pexels-photo-256541.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop',
 3120, 'Medium', 'Dr. Mechanics', '{"stress", "strain", "materials"}', false);

-- Create some sample topics for Engineering Mathematics
DO $$
DECLARE
    math_subject_id uuid;
    linear_algebra_topic_id uuid;
    calculus_topic_id uuid;
    differential_eq_topic_id uuid;
BEGIN
    -- Get the Engineering Mathematics subject ID
    SELECT id INTO math_subject_id FROM subjects WHERE code = 'MATH';
    
    -- Insert topics for Engineering Mathematics
    INSERT INTO topics (subject_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (math_subject_id, 'Linear Algebra', 'Matrix operations and systems of equations', 15, 'Medium', 1),
    (math_subject_id, 'Calculus', 'Single and multivariable calculus', 25, 'Hard', 2),
    (math_subject_id, 'Differential Equations', 'First and higher order differential equations', 20, 'Hard', 3),
    (math_subject_id, 'Complex Variables', 'Analytic functions and complex analysis', 12, 'Hard', 4),
    (math_subject_id, 'Probability and Statistics', 'Probability theory and statistical analysis', 15, 'Medium', 5),
    (math_subject_id, 'Numerical Methods', 'Numerical solutions and computational methods', 10, 'Medium', 6);
    
    -- Get topic IDs for subtopics
    SELECT id INTO linear_algebra_topic_id FROM topics WHERE name = 'Linear Algebra' AND subject_id = math_subject_id;
    SELECT id INTO calculus_topic_id FROM topics WHERE name = 'Calculus' AND subject_id = math_subject_id;
    SELECT id INTO differential_eq_topic_id FROM topics WHERE name = 'Differential Equations' AND subject_id = math_subject_id;
    
    -- Insert subtopics for Linear Algebra
    INSERT INTO subtopics (topic_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (linear_algebra_topic_id, 'Matrix Algebra', 'Matrix operations, determinants, and inverse', 5, 'Medium', 1),
    (linear_algebra_topic_id, 'Systems of Linear Equations', 'Solving systems using various methods', 5, 'Medium', 2),
    (linear_algebra_topic_id, 'Eigenvalues and Eigenvectors', 'Characteristic equations and diagonalization', 5, 'Hard', 3);
    
    -- Insert subtopics for Calculus
    INSERT INTO subtopics (topic_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (calculus_topic_id, 'Functions of Single Variable', 'Limits, continuity, and differentiability', 8, 'Medium', 1),
    (calculus_topic_id, 'Integration', 'Definite, improper, double and triple integrals', 8, 'Hard', 2),
    (calculus_topic_id, 'Multivariable Calculus', 'Partial derivatives, Taylor series, maxima and minima', 9, 'Hard', 3);
    
    -- Insert subtopics for Differential Equations
    INSERT INTO subtopics (topic_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (differential_eq_topic_id, 'First Order Equations', 'Linear and nonlinear first order equations', 8, 'Medium', 1),
    (differential_eq_topic_id, 'Higher Order Linear Equations', 'Constant coefficients and Euler-Cauchy equations', 7, 'Hard', 2),
    (differential_eq_topic_id, 'Laplace Transforms', 'Transform methods for solving differential equations', 5, 'Medium', 3);
END $$;

-- Create sample topics for Thermodynamics
DO $$
DECLARE
    thermo_subject_id uuid;
    basic_concepts_topic_id uuid;
    first_law_topic_id uuid;
    second_law_topic_id uuid;
BEGIN
    -- Get the Thermodynamics subject ID
    SELECT id INTO thermo_subject_id FROM subjects WHERE code = 'THERMO';
    
    -- Insert topics for Thermodynamics
    INSERT INTO topics (subject_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (thermo_subject_id, 'Basic Concepts', 'Thermodynamic systems and properties', 10, 'Easy', 1),
    (thermo_subject_id, 'First Law of Thermodynamics', 'Energy conservation principle', 15, 'Medium', 2),
    (thermo_subject_id, 'Second Law of Thermodynamics', 'Entropy and irreversibility', 20, 'Hard', 3),
    (thermo_subject_id, 'Properties of Pure Substances', 'Phase diagrams and property relations', 12, 'Medium', 4),
    (thermo_subject_id, 'Thermodynamic Cycles', 'Power and refrigeration cycles', 18, 'Hard', 5);
    
    -- Get topic IDs for subtopics
    SELECT id INTO basic_concepts_topic_id FROM topics WHERE name = 'Basic Concepts' AND subject_id = thermo_subject_id;
    SELECT id INTO first_law_topic_id FROM topics WHERE name = 'First Law of Thermodynamics' AND subject_id = thermo_subject_id;
    SELECT id INTO second_law_topic_id FROM topics WHERE name = 'Second Law of Thermodynamics' AND subject_id = thermo_subject_id;
    
    -- Insert subtopics for Basic Concepts
    INSERT INTO subtopics (topic_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (basic_concepts_topic_id, 'Systems and Processes', 'Types of systems and thermodynamic processes', 5, 'Easy', 1),
    (basic_concepts_topic_id, 'Properties of Pure Substances', 'State postulate and property relations', 5, 'Medium', 2);
    
    -- Insert subtopics for First Law
    INSERT INTO subtopics (topic_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (first_law_topic_id, 'Closed Systems', 'First law for closed systems', 7, 'Medium', 1),
    (first_law_topic_id, 'Open Systems', 'Steady flow energy equation', 8, 'Medium', 2);
    
    -- Insert subtopics for Second Law
    INSERT INTO subtopics (topic_id, name, description, estimated_hours, difficulty, display_order) VALUES
    (second_law_topic_id, 'Entropy', 'Entropy principle and calculations', 10, 'Hard', 1),
    (second_law_topic_id, 'Availability and Irreversibility', 'Exergy analysis and irreversibility', 10, 'Hard', 2);
END $$;

-- Insert sample badges
INSERT INTO badges (name, description, icon, color, criteria, rarity) VALUES
('Early Bird', 'Study before 8 AM for 7 consecutive days', 'sunrise', '#f59e0b', '{"early_study_streak": 7}', 'rare'),
('Night Owl', 'Study after 10 PM for 7 consecutive days', 'moon', '#6366f1', '{"late_study_streak": 7}', 'rare'),
('Speed Demon', 'Complete a test in under 60% of allotted time', 'zap', '#dc2626', '{"test_completion_time_ratio": 0.6}', 'epic'),
('Perfectionist', 'Score 100% in 3 different tests', 'star', '#f59e0b', '{"perfect_scores": 3}', 'legendary'),
('Subject Champion', 'Complete all topics in any subject', 'crown', '#7c3aed', '{"subject_completion": 1}', 'epic');

-- Create indexes for better performance on frequently queried columns
CREATE INDEX IF NOT EXISTS idx_subjects_section ON subjects(section);
CREATE INDEX IF NOT EXISTS idx_topics_subject_display ON topics(subject_id, display_order);
CREATE INDEX IF NOT EXISTS idx_subtopics_topic_display ON subtopics(topic_id, display_order);
CREATE INDEX IF NOT EXISTS idx_learning_content_featured ON learning_content(is_featured);
CREATE INDEX IF NOT EXISTS idx_achievements_rarity ON achievements(rarity);
CREATE INDEX IF NOT EXISTS idx_badges_rarity ON badges(rarity);