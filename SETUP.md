# GATE ME Prep - Setup Guide

This guide will help you set up the GATE ME Prep app with Supabase authentication and database.

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Supabase account

## 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd gate-me-prep
npm install
```

## 2. Supabase Setup

### Create a Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up/Sign in to your account
3. Click "New Project"
4. Fill in your project details:
   - Name: "GATE ME Prep"
   - Database Password: (choose a strong password)
   - Region: (choose closest to your location)
5. Click "Create new project"

### Get Your Supabase Credentials

1. In your Supabase dashboard, go to Settings > API
2. Copy the following values:
   - Project URL
   - Anon (public) key

### Configure Environment Variables

1. Open the `.env` file in the project root
2. Replace the placeholder values:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_project_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

# Other API keys (already configured)
EXPO_PUBLIC_GEMINI_API_KEY=AIzaSyDrfKfailGMzp8D5-jof2TQSmYF9ClCHfs
EXPO_PUBLIC_YOUTUBE_API_KEY=AIzaSyDdyj3xytBBUUq2ZA-Q4MJ2tetCPCvmNs0
EXPO_PUBLIC_GOOGLE_SEARCH_ENGINE_ID=036535e2252a74c96
EXPO_PUBLIC_GOOGLE_SEARCH_API_KEY=AIzaSyC_aUP4VMFC6lXfNvr5YfHJp2ARGyvKn8w
```

## 3. Database Setup

### Run Migrations

1. Install Supabase CLI:
```bash
npm install -g supabase
```

2. Login to Supabase:
```bash
supabase login
```

3. Link your project:
```bash
supabase link --project-ref your-project-ref
```

4. Push the migrations:
```bash
supabase db push
```

Alternatively, you can run the SQL migrations manually:

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of each migration file from `supabase/migrations/` in order
4. Run each migration

## 4. Authentication Setup

The app uses Supabase Auth with the following features:
- Email/Password authentication
- User profiles
- Session management
- Row Level Security (RLS)

### Enable Email Auth

1. In your Supabase dashboard, go to Authentication > Settings
2. Make sure "Enable email confirmations" is turned OFF for development
3. You can enable it later for production

## 5. Running the App

```bash
# Start the development server
npm run dev

# Or with Expo CLI
expo start
```

## 6. Testing the App

1. Open the app in Expo Go or simulator
2. Create a new account using the Sign Up screen
3. Sign in with your credentials
4. Explore the app features

## Features Implemented

### ✅ Authentication
- Real Supabase authentication
- Sign up with email/password
- Sign in with email/password
- User session management
- Automatic navigation based on auth state

### ✅ User Profiles
- User profile creation during signup
- Profile data storage in Supabase
- User preferences management

### ✅ Data Management
- Real-time data from Supabase
- Study session tracking
- Progress monitoring
- Achievement system
- Test results storage

### ✅ UI/UX
- 4 main tabs: Home, Subjects, Tests, Profile
- Additional features accessible from Home screen
- Responsive design
- Loading states and error handling

## Troubleshooting

### Common Issues

1. **Environment variables not loading**
   - Make sure `.env` file is in the project root
   - Restart the development server after changing environment variables

2. **Supabase connection errors**
   - Verify your Supabase URL and anon key
   - Check if your Supabase project is active

3. **Migration errors**
   - Make sure you have the correct permissions
   - Run migrations in the correct order

4. **Authentication not working**
   - Check if email confirmations are disabled in development
   - Verify RLS policies are set up correctly

### Getting Help

If you encounter any issues:
1. Check the console logs for error messages
2. Verify your Supabase configuration
3. Make sure all dependencies are installed
4. Check the Supabase dashboard for any errors

## Next Steps

After setup, you can:
1. Customize the app branding and colors
2. Add more subjects and topics
3. Implement additional features like video streaming
4. Set up push notifications
5. Deploy to app stores

## Production Deployment

For production deployment:
1. Enable email confirmations in Supabase
2. Set up proper domain configuration
3. Configure app store credentials
4. Set up analytics and crash reporting
5. Implement proper error handling and logging
